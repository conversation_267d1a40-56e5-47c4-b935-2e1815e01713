#!/usr/bin/env python3

import time
from typing import Any, Dict, List, Optional

import dns.resolver
import requests
import whois

from cache import get_expiration_from_headers, load_from_cache, save_to_cache
from out import log_debug


def _extract_domain_from_url(url: str) -> str:
    """Extract domain from URL without importing domainlib to avoid circular import.

    Args:
        url: URL to extract domain from

    Returns:
        Domain name or empty string if extraction fails
    """
    if not url:
        return ""

    # Remove protocol
    if "://" in url:
        url = url.split("://", 1)[1]
    elif url.startswith("//"):
        url = url[2:]
    elif url.startswith("/"):
        return ""

    # Extract domain part (before first slash)
    domain = url.split("/", 1)[0]

    return domain


def _resolve_dns_record(domain_name: str, record_type: str) -> Dict[str, Any]:
    """Resolves a specific DNS record type for a domain, with caching."""
    cache_key = f"dns_{record_type}_{domain_name}"
    cache_data_wrapper = load_from_cache("dns_record", cache_key)
    if cache_data_wrapper:
        # Check if it was a cached error
        if cache_data_wrapper.get("error"):
            log_debug(f"Cached DNS error for {record_type} {domain_name}: {cache_data_wrapper.get('error_type')} - {cache_data_wrapper.get('message')}")
            return cache_data_wrapper  # Return the cached error data
        log_debug(f"Cache hit for DNS {record_type} {domain_name}")
        return cache_data_wrapper  # Return the cached data

    records: List[str] = []
    dns_ttl: int = 3600  # Default TTL for successful records
    error_ttl: int = 300  # Default TTL for errors if no cli_cache_time
    data_to_cache: Dict[str, Any] = {"error": False, "records": [], "ttl": dns_ttl}

    try:
        log_debug(f"DNS query for {record_type} record: {domain_name}")
        answers = dns.resolver.resolve(domain_name, record_type)
        if record_type == "CNAME":
            records = [str(getattr(rdata, "target")) for rdata in answers]
        else:  # A, AAAA
            records = [getattr(rdata, "address") for rdata in answers]

        if answers and hasattr(answers, "ttl"):
            dns_ttl = answers.ttl
        log_debug(f"DNS {record_type} response: {records}, TTL: {dns_ttl}")
        data_to_cache = {"error": False, "records": records, "ttl": dns_ttl}
        # Use dns_ttl from the record as the cache expiration time unless cli_cache_time overrides
        save_to_cache("dns_record", cache_key, data_to_cache, dns_ttl)

    except dns.resolver.NoAnswer:
        log_debug(f"DNS {record_type} query for {domain_name}: No Answer")
        data_to_cache = {
            "error": True,
            "error_type": "NoAnswer",
            "error_message": "No A record found",
            "records": [],
            "ttl": error_ttl,
        }
        save_to_cache("dns_record", cache_key, data_to_cache, error_ttl)
    except dns.resolver.NXDOMAIN:
        log_debug(f"DNS {record_type} query for {domain_name}: NXDOMAIN")
        data_to_cache = {
            "error": True,
            "error_type": "NXDOMAIN",
            "error_message": "Domain does not exist",
            "records": [],
            "ttl": error_ttl,
        }
        save_to_cache("dns_record", cache_key, data_to_cache, error_ttl)
    except Exception as e:
        log_debug(f"DNS {record_type} query failed for {domain_name}: {e}")
        data_to_cache = {
            "error": True,
            "error_type": "Exception",
            "error_message": str(e),
            "records": [],
            "ttl": error_ttl,
        }
        save_to_cache("dns_record", cache_key, data_to_cache, error_ttl)

    return data_to_cache  # Return the cached data directly


def _make_http_get_request(url: str, timeout: int = 15, allow_redirects: bool = False, wait_time: Optional[float] = None) -> Dict[str, Any]:
    """Makes an HTTP GET request with caching for the raw request/response.

    First checks if DNS records exist for the domain to avoid timeouts on non-existent domains.
    """
    cache_key = f"http_get_{url}_allow_redirects_{allow_redirects}"
    cache_data_wrapper = load_from_cache("http_request", cache_key)

    if cache_data_wrapper:
        # If it's a cached error or success, it's already structured correctly
        return cache_data_wrapper

    # Extract domain from URL for DNS checking
    domain = _extract_domain_from_url(url)

    if domain:
        # Check if domain has any DNS records (A or AAAA) before attempting HTTP request
        dns_a = _resolve_dns_record(domain, "A")
        dns_aaaa = _resolve_dns_record(domain, "AAAA")

        # If both A and AAAA records have errors (NXDOMAIN or NoAnswer), don't attempt HTTP request
        if dns_a.get("error") and dns_a.get("error_type") in ["NXDOMAIN", "NoAnswer"] and dns_aaaa.get("error") and dns_aaaa.get("error_type") in ["NXDOMAIN", "NoAnswer"]:

            log_debug(f"DNS check failed for {domain}: A={dns_a.get('error_type')}, AAAA={dns_aaaa.get('error_type')} - skipping HTTP request to {url}")

            # Create error response similar to requests.exceptions.RequestException
            dns_error_data_to_cache: Dict[str, Any] = {
                "error": True,
                "error_message": f"DNS resolution failed: {dns_a.get('error_type')} for A record, {dns_aaaa.get('error_type')} for AAAA record",
                "status_code": None,
                "headers": {},
                "url": url,
            }
            error_expiration = 300
            save_to_cache(
                "http_request",
                cache_key,
                dns_error_data_to_cache,
                error_expiration,
            )

            return dns_error_data_to_cache

    try:
        log_debug(f"HTTP GET request to {url} (allow_redirects={allow_redirects})")
        response = requests.get(url, timeout=timeout, allow_redirects=allow_redirects, stream=False)

        # Apply wait_time after request if specified
        if wait_time:
            time.sleep(wait_time)

        # Convert headers to a plain dict for JSON serialization
        response_headers = dict(response.headers)

        # Determine cache expiration from headers or default
        expiration = get_expiration_from_headers(response_headers)

        response_data_to_cache: Dict[str, Any] = {
            "error": False,  # Explicitly mark non-errors
            "status_code": response.status_code,
            "headers": response_headers,
            "url": response.url,  # Final URL after internal requests redirects (if allow_redirects=True)
        }
        save_to_cache("http_request", cache_key, response_data_to_cache, expiration)
        return response_data_to_cache
    except requests.exceptions.RequestException as e:
        log_debug(f"HTTP GET request failed for {url}: {e}")
        error_data_to_cache: Dict[str, Any] = {
            "error": True,
            "error_message": str(e),
            "status_code": None,  # Or a specific error code if applicable
            "headers": {},
            "url": url,
        }
        error_expiration = 300
        save_to_cache(
            "http_request",
            cache_key,
            error_data_to_cache,
            error_expiration,
        )

        # Apply wait_time even on failure
        if wait_time:
            log_debug(f"Waiting {wait_time}s after failed request to {url}")
            time.sleep(wait_time)
        return error_data_to_cache  # Return the error structure


def _get_whois_info(domain_name: str) -> Dict[str, Any]:
    """Gets WHOIS information for a domain, with caching.

    Args:
        domain_name: The domain name to look up

    Returns:
        Dict with WHOIS information or error details
    """
    cache_key = f"whois_{domain_name}"
    cache_data_wrapper = load_from_cache("whois", cache_key)
    if cache_data_wrapper:
        log_debug(f"Cache hit for WHOIS {domain_name}")
        return cache_data_wrapper  # Return the cached data

    whois_ttl: int = 86400  # Default TTL for WHOIS data (24 hours)
    error_ttl: int = 3600  # Default TTL for errors (1 hour)
    data_to_cache: Dict[str, Any] = {"error": False, "data": {}, "ttl": whois_ttl}

    try:
        log_debug(f"WHOIS query for domain: {domain_name}")
        whois_data = whois.whois(domain_name)

        # Extract the most important WHOIS information
        important_info = {
            "registrar": getattr(whois_data, "registrar", ""),
            "creation_date": getattr(whois_data, "creation_date", ""),
            "expiration_date": getattr(whois_data, "expiration_date", ""),
            "updated_date": getattr(whois_data, "updated_date", ""),
            "status": getattr(whois_data, "status", ""),
            "name_servers": getattr(whois_data, "name_servers", []),
        }

        # Format dates for better readability
        for date_field in ["creation_date", "expiration_date", "updated_date"]:
            if isinstance(important_info[date_field], list) and important_info[date_field]:
                important_info[date_field] = str(important_info[date_field][0])
            elif important_info[date_field]:
                important_info[date_field] = str(important_info[date_field])

        # Format name servers
        if isinstance(important_info["name_servers"], list):
            important_info["name_servers"] = ", ".join(important_info["name_servers"])

        # Format status
        if isinstance(important_info["status"], list):
            important_info["status"] = ", ".join(important_info["status"])

        data_to_cache = {"error": False, "data": important_info, "ttl": whois_ttl}
        save_to_cache("whois", cache_key, data_to_cache, whois_ttl)

    except Exception as e:
        log_debug(f"WHOIS query failed for {domain_name}: {e}")
        data_to_cache = {
            "error": True,
            "error_type": "Exception",
            "error_message": str(e),
            "data": {},
            "ttl": error_ttl,
        }
        save_to_cache("whois", cache_key, data_to_cache, error_ttl)

    return data_to_cache
