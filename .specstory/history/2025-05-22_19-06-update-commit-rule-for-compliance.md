<!-- Generated by SpecStory -->

# Update Commit Rule for Compliance (2025-05-22 21:06:20)

_**User**_

/Generate Cursor Rules uprav pravidlo @commit.mdc aby  bylo v souladu s @todo-prd-rules.mdc 

---

_**Assistant**_

Nejprve si přečtu současné pravidlo commit.mdc a todo-prd-rules.mdc, abych pochopil, jak je upravit pro soulad.

---

Read file: .cursor/rules/commit.mdc

---

Read file: .cursor/rules/todo-prd-rules.mdc

---

Nyní upravím pravidlo commit.mdc tak, aby bylo v souladu s todo-prd-rules.mdc. Přidám požadavky na aktualizaci TODO.md a kontrolní seznam před commitem.

---

```diff
  # 🚨 MANDATORY: Git Commit Rules
- - Ensure that `mise run all` is run before commit.
- - Fix all errors and warnings, rerun tests until all pass.
- - Commit all changes in English:
+ 
+ ## 📋 KONTROLNÍ SEZNAM PŘED COMMITEM
+ 
+ ### ✅ POVINNÉ KROKY
+ 1. **Aktualizuj [TODO.md](mdc:.ai/TODO.md)** - označ dokončené úkoly jako hotové
+ 2. **Žádný dokončený úkol** nesmí zůstat označen jako otevřený
+ 3. **Spusť `mise run all`** a oprav všechny chyby a varování
+ 4. **Znovu spusť testy** dokud všechny neprojdou
+ 5. **Ověř soulad** s požadavky z [PRD.md](mdc:.ai/PRD.md)
+ 
+ ### ✅ KONTROLNÍ SEZNAM
+ - [ ] Aktualizoval jsem TODO.md?
+ - [ ] Všechny dokončené úkoly jsou označeny jako hotové?
+ - [ ] Spustil jsem `mise run all`?
+ - [ ] Všechny testy prošly?
+ - [ ] Opravil jsem všechny chyby a varování?
+ - [ ] Implementace odpovídá specifikacím v PRD.md?
+ 
+ ## 💻 COMMIT PŘÍKAZ
+ 
+ Commituj všechny změny v angličtině:
  ```bash
  git add . ; git commit -m "Short summary (max 100 chars)" -m "Detailed description of changes"
  ```
  
+ ## 🔗 SOUVISEJÍCÍ PRAVIDLA
+ 
+ Toto pravidlo je součástí workflow definovaného v [todo-prd-rules.mdc](mdc:todo-prd-rules.mdc).
+ 
  
```

---

Úspěšně jsem upravil pravidlo `commit.mdc` tak, aby bylo v souladu s `todo-prd-rules.mdc`. Hlavní změny zahrnují:

## 🔄 Provedené úpravy:

1. **Přidal jsem kontrolní seznam před commitem** - strukturovaný přehled povinných kroků
2. **Zdůraznil jsem aktualizaci TODO.md** - požadavek na označení dokončených úkolů
3. **Rozšířil jsem o ověření souladu s PRD.md** - kontrola, že implementace odpovídá specifikacím
4. **Přidal jsem odkazy na související soubory** - propojení s TODO.md a PRD.md
5. **Strukturoval jsem pravidlo do logických sekcí** - lepší přehlednost a použitelnost
6. **Přidal jsem odkaz na todo-prd-rules.mdc** - jasné propojení workflow

Pravidlo nyní zajišťuje, že před každým commitem bude:
- Aktualizován TODO.md s dokončenými úkoly
- Spuštěn `mise run all` a opraveny všechny chyby
- Ověřen soulad s požadavky z PRD.md
- Dodržen strukturovaný workflow definovaný v todo-prd-rules.mdc

---

