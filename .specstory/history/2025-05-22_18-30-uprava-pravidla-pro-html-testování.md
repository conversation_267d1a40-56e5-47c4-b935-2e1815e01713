<!-- Generated by SpecStory -->

# Uprava pravidla pro HTML testování (2025-05-22 20:30:18)

_**User**_

/Generate Cursor Rules uprav pravidlo @html-testing.mdc aby ti bylo srozumitelne a v souladu s ostatnimi pravidly.

---

_**Assistant**_

Nejdříve si přečtu současné pravidlo pro HTML testování a pak ho upravím podle standardů ostatních pravidel.

---

Read file: .cursor/rules/html-testing.mdc

---

Nyní si přečtu některá další pravidla, abych pochopil jejich strukturu a styl:

---

<details>
            <summary>Listed directory .cursor/rules • **9** results</summary>
        
| Name |
|-------|
| 📄 `html-testing.mdc` |
| 📄 `testing-linting.mdc` |
| 📄 `app-structure.mdc` |
| 📄 `circular-imports.mdc` |
| 📄 `rules.mdc` |
| 📄 `main.mdc` |
| 📄 `python-code-quality.mdc` |
| 📄 `todo-prd-rules.mdc` |
| 📄 `python-cli.mdc` |

</details>

---

Read file: .cursor/rules/testing-linting.mdc

---

Nyní si přečtu ještě jedno pravidlo pro lepší pochopení stylu:

---

Read file: .cursor/rules/app-structure.mdc

---

Nyní upravím pravidlo pro HTML testování podle standardů ostatních pravidel:

---



---



---

