#!/usr/bin/env python3

import unittest
from unittest.mock import MagicMock, patch

import dns.resolver
import requests

from cache import init_cache

# Import the functions to test
from netlib import (
    _extract_domain_from_url,
    _make_http_get_request,
    _resolve_dns_record,
)

init_cache(disable=True)


class TestExtractDomainFromUrl(unittest.TestCase):
    """Test cases for the _extract_domain_from_url function."""

    def test_extract_domain_from_http_url(self):
        """Test extracting domain from HTTP URL."""
        result = _extract_domain_from_url("http://example.com/path")
        self.assertEqual(result, "example.com")

    def test_extract_domain_from_https_url(self):
        """Test extracting domain from HTTPS URL."""
        result = _extract_domain_from_url("https://example.com/path")
        self.assertEqual(result, "example.com")

    def test_extract_domain_from_protocol_relative_url(self):
        """Test extracting domain from protocol-relative URL."""
        result = _extract_domain_from_url("//example.com/path")
        self.assertEqual(result, "example.com")

    def test_extract_domain_from_domain_only(self):
        """Test extracting domain from domain-only string."""
        result = _extract_domain_from_url("example.com")
        self.assertEqual(result, "example.com")

    def test_extract_domain_with_port(self):
        """Test extracting domain with port."""
        result = _extract_domain_from_url("http://example.com:8080/path")
        self.assertEqual(result, "example.com:8080")

    def test_extract_domain_from_empty_string(self):
        """Test extracting domain from empty string."""
        result = _extract_domain_from_url("")
        self.assertEqual(result, "")

    def test_extract_domain_from_relative_path(self):
        """Test extracting domain from relative path returns empty."""
        result = _extract_domain_from_url("/path/to/resource")
        self.assertEqual(result, "")


class TestDnsResolve(unittest.TestCase):
    """Test cases for the _resolve_dns_record function."""

    @patch("dns.resolver.resolve")
    def test_resolve_dns_record_a(self, mock_resolve):
        """Test resolving A records."""
        # Setup mock
        mock_answer = MagicMock()
        mock_answer.ttl = 3600
        mock_rdata = MagicMock()
        mock_rdata.address = "***********"
        mock_answer.__iter__.return_value = [mock_rdata]
        mock_resolve.return_value = mock_answer

        # Call the function
        result = _resolve_dns_record("example.com", "A")

        # Assertions
        mock_resolve.assert_called_once_with("example.com", "A")
        self.assertEqual(result["records"], ["***********"])
        self.assertEqual(result["ttl"], 3600)
        self.assertEqual(result["error"], False)

    @patch("dns.resolver.resolve")
    def test_resolve_dns_record_cname(self, mock_resolve):
        """Test resolving CNAME records."""
        # Setup mock
        mock_answer = MagicMock()
        mock_answer.ttl = 1800
        mock_rdata = MagicMock()
        mock_rdata.target = "target.example.com"
        mock_answer.__iter__.return_value = [mock_rdata]
        mock_resolve.return_value = mock_answer

        # Call the function
        result = _resolve_dns_record("example.com", "CNAME")

        # Assertions
        mock_resolve.assert_called_once_with("example.com", "CNAME")
        self.assertEqual(result["records"], ["target.example.com"])
        self.assertEqual(result["ttl"], 1800)
        self.assertEqual(result["error"], False)

    @patch("dns.resolver.resolve")
    def test_resolve_dns_record_no_answer(self, mock_resolve):
        """Test handling of NoAnswer exception."""
        # Setup mock to raise NoAnswer
        mock_resolve.side_effect = dns.resolver.NoAnswer()

        # Call the function
        result = _resolve_dns_record("example.com", "A")

        # Assertions
        self.assertEqual(result["records"], [])
        self.assertEqual(result["ttl"], 300)  # Default error TTL
        self.assertEqual(result["error"], True)
        self.assertEqual(result["error_type"], "NoAnswer")

    @patch("dns.resolver.resolve")
    def test_resolve_dns_record_nxdomain(self, mock_resolve):
        """Test handling of NXDOMAIN exception."""
        # Setup mock to raise NXDOMAIN
        mock_resolve.side_effect = dns.resolver.NXDOMAIN()

        # Call the function
        result = _resolve_dns_record("example.com", "A")

        # Assertions
        self.assertEqual(result["records"], [])
        self.assertEqual(result["ttl"], 300)  # Default error TTL
        self.assertEqual(result["error"], True)
        self.assertEqual(result["error_type"], "NXDOMAIN")


class TestHttpGetRequest(unittest.TestCase):
    """Test cases for the _make_http_get_request function."""

    @patch("requests.get")
    def test_make_http_get_request_success(self, mock_get):
        """Test successful HTTP GET request."""
        # Setup mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {
            "Content-Type": "text/html",
            "Cache-Control": "max-age=3600",
        }
        mock_response.url = "http://example.com"
        mock_get.return_value = mock_response

        # Call the function
        result = _make_http_get_request("http://example.com")

        # Assertions
        mock_get.assert_called_once_with("http://example.com", timeout=15, allow_redirects=False, stream=False)
        self.assertEqual(result["status_code"], 200)
        self.assertEqual(
            result["headers"],
            {"Content-Type": "text/html", "Cache-Control": "max-age=3600"},
        )
        self.assertEqual(result["url"], "http://example.com")
        self.assertFalse(result["error"])

    @patch("requests.get")
    def test_make_http_get_request_with_redirect(self, mock_get):
        """Test HTTP GET request with redirect."""
        # Setup mock
        mock_response = MagicMock()
        mock_response.status_code = 301
        mock_response.headers = {
            "Location": "https://example.com",
            "Cache-Control": "max-age=3600",
        }
        mock_response.url = "http://example.com"
        mock_get.return_value = mock_response

        # Call the function
        result = _make_http_get_request("http://example.com")

        # Assertions
        mock_get.assert_called_once_with("http://example.com", timeout=15, allow_redirects=False, stream=False)
        self.assertEqual(result["status_code"], 301)
        self.assertEqual(
            result["headers"],
            {"Location": "https://example.com", "Cache-Control": "max-age=3600"},
        )
        self.assertEqual(result["url"], "http://example.com")
        self.assertFalse(result["error"])

    @patch("requests.get")
    def test_make_http_get_request_exception(self, mock_get):
        """Test handling of request exception."""
        # Setup mock to raise exception
        mock_get.side_effect = requests.exceptions.RequestException("Connection error")

        # Call the function
        result = _make_http_get_request("http://example.com")

        # Assertions
        self.assertTrue(result["error"])
        self.assertEqual(result["error_message"], "Connection error")
        self.assertIsNone(result["status_code"])
        self.assertEqual(result["headers"], {})
        self.assertEqual(result["url"], "http://example.com")

    @patch("netlib._resolve_dns_record")
    @patch("requests.get")
    def test_make_http_get_request_dns_optimization_skip(self, mock_get, mock_dns):
        """Test that HTTP request is skipped when domain has no DNS records."""
        # Setup DNS mock to return NXDOMAIN for both A and AAAA records
        mock_dns.side_effect = [
            {
                "error": True,
                "error_type": "NXDOMAIN",
                "error_message": "Domain does not exist",
                "records": [],
                "ttl": 300,
            },
            {
                "error": True,
                "error_type": "NXDOMAIN",
                "error_message": "Domain does not exist",
                "records": [],
                "ttl": 300,
            },
        ]

        # Call the function
        result = _make_http_get_request("http://nonexistent.example.com")

        # Assertions
        # DNS should be checked twice (A and AAAA records)
        self.assertEqual(mock_dns.call_count, 2)
        mock_dns.assert_any_call("nonexistent.example.com", "A")
        mock_dns.assert_any_call("nonexistent.example.com", "AAAA")

        # HTTP request should NOT be made
        mock_get.assert_not_called()

        # Result should be an error
        self.assertTrue(result["error"])
        self.assertIn("DNS resolution failed", result["error_message"])
        self.assertIsNone(result["status_code"])
        self.assertEqual(result["headers"], {})
        self.assertEqual(result["url"], "http://nonexistent.example.com")

    @patch("netlib._resolve_dns_record")
    @patch("requests.get")
    def test_make_http_get_request_dns_optimization_proceed(self, mock_get, mock_dns):
        """Test that HTTP request proceeds when domain has DNS records."""
        # Setup DNS mock to return valid A record and NXDOMAIN for AAAA
        mock_dns.side_effect = [
            {
                "error": False,
                "records": ["***********"],
                "ttl": 3600,
            },
            {
                "error": True,
                "error_type": "NoAnswer",
                "error_message": "No AAAA record found",
                "records": [],
                "ttl": 300,
            },
        ]

        # Setup HTTP mock
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "text/html"}
        mock_response.url = "http://example.com"
        mock_get.return_value = mock_response

        # Call the function
        result = _make_http_get_request("http://example.com")

        # Assertions
        # DNS should be checked twice (A and AAAA records)
        self.assertEqual(mock_dns.call_count, 2)
        mock_dns.assert_any_call("example.com", "A")
        mock_dns.assert_any_call("example.com", "AAAA")

        # HTTP request SHOULD be made because A record exists
        mock_get.assert_called_once_with("http://example.com", timeout=15, allow_redirects=False, stream=False)

        # Result should be successful
        self.assertFalse(result["error"])
        self.assertEqual(result["status_code"], 200)
        self.assertEqual(result["url"], "http://example.com")


if __name__ == "__main__":
    unittest.main()
