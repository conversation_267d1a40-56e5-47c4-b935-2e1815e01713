---
description: <PERSON><PERSON><PERSON><PERSON> a Linting - MUSÍŠ číst před psaním testů
globs: 
alwaysApply: false
---
# Testování a Linting

## 🎯 ÚČEL

Definuje standardy pro kvalitu kódu, testování a automatické kontroly pomocí `mise run all`.

## 🧪 TESTOVACÍ STRATEGIE

### ✅ POVINNÉ TESTY
1. **Unit testy** - každá funkce má test
2. **Integration testy** - testován<PERSON> spolupráce modulů
3. **Edge case testy** - hraniční případy a chybové stavy
4. **Regression testy** - prevence návratu chyb

### 📁 STRUKTURA TESTŮ
```
tests/
├── test_domainlib.py      # Testy pro domainlib.py
├── test_netlib.py         # Testy pro netlib.py
├── test_logic.py          # Testy pro logic.py
├── test_main.py           # Testy pro main script
├── conftest.py            # Pytest konfigurace
└── fixtures/              # Test data
    ├── sample_domains.txt
    └── mock_responses.json
```

## 🔧 MISE KONFIGURACE

### ✅ POVINNÉ NÁSTROJE
- **pytest** - spouštění testů
- **mypy** - type checking
- **pylint** - code quality
- **black** - code formatting
- **isort** - import sorting

### 📋 KONTROLNÍ SEZNAM `mise run all`

Po každé změně kódu **MUSÍ** projít:
```bash
mise run all
```

Který spustí:
1. **Formatting**: `black .` a `isort .`
2. **Type checking**: `mypy .`
3. **Linting**: `pylint src/`
4. **Testing**: `pytest tests/`

## 🚫 NULOVÁ TOLERANCE

### ❌ NEPŘÍPUSTNÉ STAVY
- Failing testy
- MyPy chyby
- Pylint chyby (score < 9.0)
- Neformátovaný kód
- Chybějící type hints

### ⚠️ VAROVÁNÍ K ŘEŠENÍ
- Pylint varování (cíl: score 10.0)
- Nedostatečné pokrytí testy (cíl: >90%)
- Komplexní funkce (cíl: cyclomatic complexity < 10)

## 📝 STANDARDY PSANÍ TESTŮ

### ✅ DOBRÝ TEST
```python
def test_extract_domain_from_url_valid_input():
    """Test extraction with valid URL."""
    # Arrange
    url = "https://example.com/path"
    expected = "example.com"
    
    # Act
    result = extract_domain_from_url(url)
    
    # Assert
    assert result == expected

def test_extract_domain_from_url_invalid_input():
    """Test extraction with invalid URL."""
    # Arrange
    url = ""
    expected = ""
    
    # Act
    result = extract_domain_from_url(url)
    
    # Assert
    assert result == expected
```

### 🚫 ŠPATNÝ TEST
```python
def test_function():  # Nespecifický název
    result = some_function("input")  # Bez arrange/act/assert
    assert result  # Nespecifická assertion
```

## 🔍 TYPE HINTS STANDARDY

### ✅ POVINNÉ TYPE HINTS
```python
from typing import List, Dict, Optional, Union

def process_domains(
    domains: List[str], 
    config: Dict[str, str]
) -> Optional[Dict[str, bool]]:
    """Process list of domains with configuration."""
    pass
```

### 🚫 ZAKÁZÁNO
```python
def process_domains(domains, config):  # Bez type hints
    pass
```

## 📊 METRIKY KVALITY

### ✅ CÍLOVÉ HODNOTY
- **Test coverage**: > 90%
- **Pylint score**: 10.0
- **MyPy**: 0 errors
- **Cyclomatic complexity**: < 10 per function
- **Function length**: < 50 lines

### 📋 KONTROLNÍ SEZNAM PŘED COMMITEM

- [ ] `mise run all` prošel bez chyb
- [ ] Všechny nové funkce mají testy
- [ ] Všechny funkce mají type hints
- [ ] Všechny funkce mají docstrings
- [ ] Kód je čitelný a dobře komentovaný
- [ ] Žádné TODO komentáře v produkčním kódu

## 🛠️ ŘEŠENÍ ČASTÝCH PROBLÉMŮ

### MyPy Chyby
```python
# ❌ ŠPATNĚ
def process(data):
    return data.upper()

# ✅ SPRÁVNĚ  
def process(data: str) -> str:
    return data.upper()
```

### Pylint Varování
```python
# ❌ ŠPATNĚ - příliš dlouhý řádek
very_long_variable_name = some_function_with_very_long_name(parameter1, parameter2, parameter3)

# ✅ SPRÁVNĚ
very_long_variable_name = some_function_with_very_long_name(
    parameter1, 
    parameter2, 
    parameter3
)
```

### Test Failures
1. **Izoluj problém** - spusť konkrétní test
2. **Debug systematicky** - použij print/debugger
3. **Oprav root cause** - ne jen symptom
4. **Přidej regression test** - zabraň návratu chyby

## 🎯 AUTOMATIZACE

### ✅ PRE-COMMIT HOOKS
```bash
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: mise-run-all
        name: Run all quality checks
        entry: mise run all
        language: system
        pass_filenames: false
```

**Pamatuj: Kvalita kódu není volitelná - je to základní požadavek!**
