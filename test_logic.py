#!/usr/bin/env python3

import unittest
from typing import Set

from domainlib import RedirectResult
from logic import LogicContext, process_domain_logic


class MockLogicContext(LogicContext):
    """Test implementation of LogicContext for unit tests."""

    def __init__(
        self,
        our_ips: Set[str] = None,
        our_ipv6s: Set[str] = None,
        our_domains: Set[str] = None,
        **kwargs,
    ):
        """Initialize the test logic context with test data.

        Args:
            our_ips: Set of our IPv4 addresses
            our_ipv6s: Set of our IPv6 addresses
            our_domains: Set of our domains
            **kwargs: Additional arguments to pass to the parent class
        """
        super().__init__(**kwargs)
        self.our_ips = our_ips or set()
        self.our_ipv6s = our_ipv6s or set()
        self.our_domains = our_domains or set()

    def check_in_our_ips(self, ip: str) -> bool:
        """Check if an IP is in our IPs set.

        Args:
            ip: The IP address to check

        Returns:
            bool: True if the IP is in our IPs set, False otherwise
        """
        return ip in self.our_ips

    def check_in_our_ipv6s(self, ipv6: str) -> bool:
        """Check if an IPv6 is in our IPv6s set.

        Args:
            ipv6: The IPv6 address to check

        Returns:
            bool: True if the IPv6 is in our IPv6s set, False otherwise
        """
        return ipv6 in self.our_ipv6s

    def check_in_our_domains(self, domain: str) -> bool:
        """Check if a domain is in our domains set.

        Args:
            domain: The domain to check

        Returns:
            bool: True if the domain is in our domains set, False otherwise
        """
        # Direct check
        if domain in self.our_domains:
            return True

        # Wildcard check
        parts = domain.split(".", 1)
        if len(parts) > 1:
            wildcard_domain = "*." + parts[1]
            if wildcard_domain in self.our_domains:
                return True

        return False


class TestProcessDomainLogic(unittest.TestCase):
    """Test cases for the process_domain_logic function."""

    def test_example_com_domain(self):
        """Test processing example.com domain."""
        # Setup test data
        domain = "example.com"

        # Mock DNS records
        dns_a = {"records": ["*************"]}
        dns_aaaa = {"records": ["2606:2800:220:1:248:1893:25c8:1946"]}
        dns_cname = {"records": []}

        # Mock redirect result
        redirect_result = RedirectResult(
            is_redirected=False,
            final_domain="example.com",
            final_protocol="http",
            redirect_chain=[],
        )

        # Mock HTTP responses
        https_direct = {
            "status_code": 200,
            "headers": {"Server": "cloudflare"},
            "error": None,
        }

        http_direct = {
            "status_code": 200,
            "headers": {"Server": "cloudflare"},
            "error": None,
        }

        # Create test context
        context = MockLogicContext(
            our_ips={"127.0.0.1"},
            our_ipv6s=set(),
            our_domains={"example.com", "*.example.com"},
        )

        # Mock WHOIS info
        whois_info = {
            "error": False,
            "data": {
                "registrar": "Test Registrar",
                "creation_date": "2023-01-01",
                "expiration_date": "2024-01-01",
                "name_servers": "ns1.example.com, ns2.example.com",
            },
        }

        # Call the function
        result = process_domain_logic(
            domain=domain,
            dns_a=dns_a,
            dns_aaaa=dns_aaaa,
            dns_cname=dns_cname,
            redirect_result=redirect_result,
            https_direct=https_direct,
            http_direct=http_direct,
            context=context,
            whois_info=whois_info,
        )

        # Verify results
        self.assertEqual(result["domain"].value, "example.com")
        self.assertEqual(result["reversed_domain"].value, "com.example")
        self.assertEqual(result["ip"].value, "*************")
        self.assertEqual(result["ipv6"].value, "2606:2800:220:1:248:1893:25c8:1946")
        self.assertEqual(result["cname"].value, "")
        self.assertEqual(result["our"].value, "no")  # Not in our IPs
        self.assertEqual(result["cdn"].value, "yes")  # Using Cloudflare
        self.assertEqual(result["cdn_provider"].value, "cloudflare")
        self.assertEqual(result["https"].value, "yes")  # HTTPS works
        self.assertEqual(result["https_redirect"].value, "no")  # No HTTPS redirect

    def test_wildcard_example_com_domain(self):
        """Test processing *.example.com domain."""
        # Setup test data
        domain = "*.example.com"

        # Mock DNS records
        dns_a = {"records": ["*************"]}
        dns_aaaa = {"records": ["2606:2800:220:1:248:1893:25c8:1946"]}
        dns_cname = {"records": []}

        # Mock redirect result
        redirect_result = RedirectResult(
            is_redirected=True,
            final_domain="example.com",
            final_protocol="https",
            redirect_chain=["http://www.example.com", "https://example.com"],
        )

        # Mock HTTP responses
        https_direct = {
            "status_code": 200,
            "headers": {"Server": "ECS (dcb/7F84)"},
            "error": None,
        }

        http_direct = {
            "status_code": 301,
            "headers": {"Server": "ECS (dcb/7F84)", "Location": "https://example.com"},
            "error": None,
        }

        # Create test context
        context = MockLogicContext(
            our_ips={"127.0.0.1"},
            our_ipv6s=set(),
            our_domains={"example.com", "*.example.com"},
        )

        # Mock WHOIS info
        whois_info = {
            "error": False,
            "data": {
                "registrar": "Test Registrar",
                "creation_date": "2023-01-01",
                "expiration_date": "2024-01-01",
                "name_servers": "ns1.example.com, ns2.example.com",
            },
        }

        # Call the function
        result = process_domain_logic(
            domain=domain,
            dns_a=dns_a,
            dns_aaaa=dns_aaaa,
            dns_cname=dns_cname,
            redirect_result=redirect_result,
            https_direct=https_direct,
            http_direct=http_direct,
            context=context,
            whois_info=whois_info,
        )

        # Verify results
        self.assertEqual(result["domain"].value, "*.example.com")
        self.assertEqual(result["reversed_domain"].value, "com.example.*")
        self.assertEqual(result["ip"].value, "*************")
        self.assertEqual(result["ipv6"].value, "2606:2800:220:1:248:1893:25c8:1946")
        self.assertEqual(result["cname"].value, "")
        self.assertEqual(result["our"].value, "no")  # Not in our IPs
        self.assertEqual(result["cdn"].value, "no")  # No CDN detected
        self.assertEqual(result["redirect"].value, "example.com")
        self.assertEqual(result["our_redirect"].value, "yes")  # Redirects to our domain
        self.assertEqual(result["https"].value, "yes")  # HTTPS works
        self.assertEqual(result["https_redirect"].value, "yes")  # HTTPS redirect


if __name__ == "__main__":
    unittest.main()
