<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Domain Review Results</title>

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/3.1.2/css/buttons.dataTables.min.css">

<style>
    /* Container and layout */
    body {
        font-family: Arial, sans-serif;
        margin: 20px;
        background-color: #f5f5f5;
    }

    .container {
        background-color: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    h1 {
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }

    /* Table styling */
    table.dataTable {
        width: 100% !important;
        border-collapse: collapse;
        margin-top: 20px;
    }

    table.dataTable thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        padding: 15px 8px;
        text-align: left;
        border: none;
        position: relative;
    }

    table.dataTable tbody td {
        padding: 12px 8px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
    }

    table.dataTable tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* Status styling */
    .status-yes {
        color: #28a745;
        font-weight: bold;
    }

    .status-no {
        color: #dc3545;
        font-weight: bold;
    }

    .status-empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Links */
    a {
        color: #007bff;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }

    /* DataTables controls */
    .dataTables_wrapper {
        margin-top: 20px;
    }

    .dataTables_length,
    .dataTables_filter {
        margin-bottom: 20px;
    }

    .dataTables_info,
    .dataTables_paginate {
        margin-top: 20px;
    }

    /* Buttons styling */
    .dt-buttons {
        margin-bottom: 20px;
    }

    .dt-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        margin-right: 5px;
        border-radius: 4px;
        cursor: pointer;
    }

    .dt-button:hover {
        background: #0056b3;
    }

    /* Preset management */
    .preset-controls {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }

    .preset-controls h3 {
        margin: 0 0 10px 0;
        color: #495057;
        font-size: 16px;
    }

    .preset-group {
        display: inline-block;
        margin-right: 20px;
        margin-bottom: 10px;
    }

    .preset-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
    }

    .preset-group input,
    .preset-group select,
    .preset-group button {
        padding: 6px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }

    .preset-group button {
        background: #28a745;
        color: white;
        border: none;
        cursor: pointer;
        margin-left: 5px;
    }

    .preset-group button:hover {
        background: #218838;
    }

    .preset-group button.delete {
        background: #dc3545;
    }

    .preset-group button.delete:hover {
        background: #c82333;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .preset-group {
            display: block;
            margin-right: 0;
            margin-bottom: 15px;
        }

        .preset-group input,
        .preset-group select,
        .preset-group button {
            width: 100%;
            margin-bottom: 5px;
            margin-left: 0;
        }

        .container {
            margin: 10px;
            padding: 15px;
        }

        table.dataTable thead th {
            padding: 10px 5px;
            font-size: 14px;
        }

        table.dataTable tbody td {
            padding: 8px 5px;
            font-size: 13px;
        }
    }

    /* Column search inputs */
    .column-search {
        width: 100%;
        margin-top: 5px;
        padding: 3px;
        font-size: 12px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }

    /* DataTables responsive adjustments */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 10px 0;
    }

    @media (max-width: 768px) {
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            float: none;
            text-align: center;
        }

        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            float: none;
            text-align: center;
        }
    }
</style>

</head>
<body>

{# Generic macro to render any table cell with automatic link detection and popup support #}
{%- macro render_cell(field, cell_data, domain_row) -%}
<td>
{%- if cell_data -%}
{%- set value = cell_data.value or '' -%}
{%- set popup = cell_data.popup -%}
{%- set title_attr = ' title="' + popup + '"' if popup else '' -%}
{%- if field == 'domain' -%}
{%- set https_cell = domain_row.get('https') -%}
{%- set protocol = 'https' if https_cell and https_cell.value == 'yes' else 'http' -%}
<a href="{{ protocol }}://{{ value }}" target="_blank"{{ title_attr }}>{{ value }}</a>
{%- elif field == 'ip' and value and value not in ['(NXDOMAIN)', ''] and '(NoAnswer)' not in value -%}
<a href="https://ipinfo.io/{{ value }}" target="_blank"{{ title_attr }}>{{ value }}</a>
{%- elif field == 'redirect' and value and value not in ['(NXDOMAIN)', ''] -%}
<a href="https://{{ value }}" target="_blank"{{ title_attr }}>{{ value }}</a>
{%- elif value == 'yes' -%}
<span class="status-yes"{{ title_attr }}>{{ value }}</span>
{%- elif value == 'no' and field in ['our', 'our_redirect', 'https', 'header'] -%}
<span class="status-no"{{ title_attr }}>{{ value }}</span>
{%- elif value in ['', '(NXDOMAIN)'] or '(NoAnswer)' in value -%}
<span class="status-empty"{{ title_attr }}>{{ value }}</span>
{%- else -%}
<span{{ title_attr }}>{{ value }}</span>
{%- endif -%}
{%- endif -%}
</td>
{%- endmacro -%}

<div class="container">
    <h1>Domain Review Results</h1>

    <!-- Preset Management Controls -->
    <div class="preset-controls">
        <h3>Column Presets</h3>

        <div class="preset-group">
            <label>Load Preset:</label>
            <select id="presetSelect">
                <option value="">Select a preset...</option>
                <option value="default">Default View</option>
                <option value="minimal">Minimal View</option>
                <option value="full">Full View</option>
            </select>
            <button onclick="loadPreset()">Load</button>
        </div>

        <div class="preset-group">
            <label>Save Current View:</label>
            <input type="text" id="presetName" placeholder="Enter preset name...">
            <button onclick="savePreset()">Save</button>
        </div>

        <div class="preset-group">
            <label>Delete Preset:</label>
            <select id="deletePresetSelect">
                <option value="">Select preset to delete...</option>
            </select>
            <button class="delete" onclick="deletePreset()">Delete</button>
        </div>
    </div>

    <table id="domainsTable" class="display" style="width:100%">
        <thead>
            <tr>
                {%- for field in fieldnames %}
                <th data-column="{{ field }}">
                    <div class="header-content">
                        <span class="header-title">{{ field }}</span>
                    </div>
                </th>
                {%- endfor %}
            </tr>
        </thead>
        <tbody>
            {%- for domain_row in data %}
            <tr>
                {%- for field in fieldnames %}
                {%- set cell_data = domain_row.get(field) %}
                {{ render_cell(field, cell_data, domain_row) }}
                {%- endfor %}
            </tr>
            {%- endfor %}
        </tbody>
    </table>
</div>

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.colVis.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#domainsTable').DataTable({
        // Basic configuration
        paging: true,
        pageLength: 25,
        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
        ordering: true,
        info: true,
        searching: true,
        responsive: true,

        // Layout configuration
        layout: {
            topStart: 'buttons',
            topEnd: 'search',
            bottomStart: 'info',
            bottomEnd: 'paging'
        },

        // Buttons configuration
        buttons: [
            {
                extend: 'colvis',
                text: 'Show/Hide Columns',
                columns: ':not(.noVis)',
                columnText: function(dt, idx, title) {
                    // Get the column title from the header
                    var columnTitle = $(dt.column(idx).header()).find('.header-title').text();
                    return columnTitle || title;
                }
            }
        ],

        // Column definitions
        columnDefs: [
            {
                targets: '_all',
                className: 'dt-body-left'
            }
        ],

        // Scrolling configuration
        scrollX: true,
        scrollCollapse: true,

        // Search configuration
        search: {
            regex: true,
            smart: false
        },

        // Initialize complete callback
        initComplete: function() {
            var api = this.api();

            // Add individual column search inputs
            api.columns().every(function() {
                var column = this;
                var header = $(column.header());
                var title = header.find('.header-title').text() || header.text();

                // Create search input
                var input = $('<input type="text" placeholder="Search ' + title + '" class="column-search">')
                    .appendTo(header)
                    .on('keyup change clear', function() {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });

    // Initialize presets
    initializePresets();
    updatePresetSelects();
});

// Preset Management Functions
function initializePresets() {
    // Define default presets if they don't exist
    var defaultPresets = {
        'default': {
            name: 'Default View',
            columns: ['domain', 'ip', 'ipv6', 'our', 'redirect', 'https', 'cdn']
        },
        'minimal': {
            name: 'Minimal View',
            columns: ['domain', 'ip', 'our', 'https']
        },
        'full': {
            name: 'Full View',
            columns: {{ fieldnames | tojson | safe }}
        }
    };

    // Save default presets if not already saved
    for (var key in defaultPresets) {
        var storageKey = 'datatables_preset_' + key;
        if (!localStorage.getItem(storageKey)) {
            localStorage.setItem(storageKey, JSON.stringify(defaultPresets[key]));
        }
    }
}

function updatePresetSelects() {
    var presetSelect = $('#presetSelect');
    var deleteSelect = $('#deletePresetSelect');

    // Clear custom options (keep default ones)
    presetSelect.find('option').each(function() {
        if (!['', 'default', 'minimal', 'full'].includes($(this).val())) {
            $(this).remove();
        }
    });
    deleteSelect.empty().append('<option value="">Select preset to delete...</option>');

    // Add saved presets
    for (var i = 0; i < localStorage.length; i++) {
        var key = localStorage.key(i);
        if (key.startsWith('datatables_preset_')) {
            var presetKey = key.replace('datatables_preset_', '');
            var preset = JSON.parse(localStorage.getItem(key));

            // Add to load select if not a default preset
            if (!['default', 'minimal', 'full'].includes(presetKey)) {
                presetSelect.append('<option value="' + presetKey + '">' + preset.name + '</option>');
            }

            // Add to delete select
            deleteSelect.append('<option value="' + presetKey + '">' + preset.name + '</option>');
        }
    }
}

function savePreset() {
    var name = $('#presetName').val().trim();
    if (!name) {
        alert('Please enter a preset name');
        return;
    }

    var table = $('#domainsTable').DataTable();
    var visibleColumns = [];

    // Get currently visible columns
    table.columns().every(function(index) {
        if (this.visible()) {
            var header = $(this.header()).find('.header-title').text().trim();
            if (!header) {
                header = $(this.header()).text().trim();
            }
            visibleColumns.push(header);
        }
    });

    var preset = {
        name: name,
        columns: visibleColumns
    };

    // Save to localStorage
    var key = 'datatables_preset_' + name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    localStorage.setItem(key, JSON.stringify(preset));

    // Update selects
    updatePresetSelects();

    // Clear input
    $('#presetName').val('');

    alert('Preset "' + name + '" saved successfully!');
}

function loadPreset() {
    var presetKey = $('#presetSelect').val();
    if (!presetKey) {
        alert('Please select a preset to load');
        return;
    }

    var storageKey = 'datatables_preset_' + presetKey;
    var preset = JSON.parse(localStorage.getItem(storageKey));

    if (!preset) {
        alert('Preset not found');
        return;
    }

    var table = $('#domainsTable').DataTable();

    // Hide all columns first
    table.columns().every(function() {
        this.visible(false);
    });

    // Show only columns in the preset
    preset.columns.forEach(function(columnName) {
        table.columns().every(function() {
            var header = $(this.header()).find('.header-title').text().trim();
            if (!header) {
                header = $(this.header()).text().trim();
            }
            if (header === columnName) {
                this.visible(true);
            }
        });
    });

    alert('Preset "' + preset.name + '" loaded successfully!');
}

function deletePreset() {
    var presetKey = $('#deletePresetSelect').val();
    if (!presetKey) {
        alert('Please select a preset to delete');
        return;
    }

    // Don't allow deletion of default presets
    if (['default', 'minimal', 'full'].includes(presetKey)) {
        alert('Cannot delete default presets');
        return;
    }

    if (!confirm('Are you sure you want to delete this preset?')) {
        return;
    }

    var storageKey = 'datatables_preset_' + presetKey;
    localStorage.removeItem(storageKey);

    updatePresetSelects();
    alert('Preset deleted successfully!');
}
</script>

</body>
</html>