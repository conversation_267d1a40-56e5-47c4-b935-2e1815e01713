<!DOCTYPE html>
<html>
<head>
<meta charset="UTF-8">
<title>Domain Review Results</title>

<!-- DataTables CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.1.8/css/dataTables.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/3.1.2/css/buttons.dataTables.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/fixedheader/4.0.1/css/fixedHeader.dataTables.min.css">

<style>
    /* Container and layout - Full page, no margins */
    html, body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        height: 100vh;
        background-color: white;
        overflow: hidden;
    }

    .container {
        background-color: white;
        padding: 0;
        margin: 0;
        height: 100vh;
        display: flex;
        flex-direction: column;
    }

    h1 {
        color: #333;
        margin: 5px 0;
        text-align: center;
        font-size: 20px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 15px;
    }

    .header-toggle {
        background: #007bff;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        white-space: nowrap;
    }

    .header-toggle:hover {
        background: #0056b3;
    }

    /* Table styling - Full height with scroll */
    .table-wrapper {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    table.dataTable {
        width: 100% !important;
        border-collapse: collapse;
        margin: 0;
    }

    table.dataTable thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        padding: 15px 8px;
        text-align: left;
        border: none;
        position: relative;
    }

    table.dataTable tbody td {
        padding: 12px 8px;
        border-bottom: 1px solid #eee;
        vertical-align: top;
    }

    table.dataTable tbody tr:hover {
        background-color: #f8f9fa;
    }

    /* Status styling */
    .status-yes {
        color: #28a745;
        font-weight: bold;
    }

    .status-no {
        color: #dc3545;
        font-weight: bold;
    }

    .status-empty {
        color: #6c757d;
        font-style: italic;
    }

    /* Links */
    a {
        color: #007bff;
        text-decoration: none;
    }

    a:hover {
        text-decoration: underline;
    }

    /* DataTables controls - Full height layout */
    .dataTables_wrapper {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .dataTables_length,
    .dataTables_filter {
        margin: 5px 0;
    }

    .dataTables_info,
    .dataTables_paginate {
        margin: 5px 0;
    }

    .dataTables_scroll {
        flex: 1;
        overflow: hidden;
    }

    .dataTables_scrollBody {
        height: calc(100vh - 200px) !important;
        overflow-y: auto !important;
    }

    /* Buttons styling */
    .dt-buttons {
        margin-bottom: 20px;
    }

    .dt-button {
        background: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        margin-right: 5px;
        border-radius: 4px;
        cursor: pointer;
    }

    .dt-button:hover {
        background: #0056b3;
    }

    /* Preset management - Compact and collapsible */
    .preset-controls {
        margin: 0;
        padding: 8px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        flex-shrink: 0;
        display: none; /* Hidden by default */
    }

    .preset-controls.show {
        display: block;
    }

    .preset-controls h3 {
        margin: 0 0 8px 0;
        color: #495057;
        font-size: 14px;
    }

    .preset-group {
        display: inline-block;
        margin-right: 15px;
        margin-bottom: 8px;
    }

    .preset-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
    }

    .preset-group input,
    .preset-group select,
    .preset-group button {
        padding: 6px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }

    .preset-group button {
        background: #28a745;
        color: white;
        border: none;
        cursor: pointer;
        margin-left: 5px;
    }

    .preset-group button:hover {
        background: #218838;
    }

    .preset-group button.delete {
        background: #dc3545;
    }

    .preset-group button.delete:hover {
        background: #c82333;
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .preset-group {
            display: block;
            margin-right: 0;
            margin-bottom: 15px;
        }

        .preset-group input,
        .preset-group select,
        .preset-group button {
            width: 100%;
            margin-bottom: 5px;
            margin-left: 0;
        }

        .container {
            margin: 10px;
            padding: 15px;
        }

        table.dataTable thead th {
            padding: 10px 5px;
            font-size: 14px;
        }

        table.dataTable tbody td {
            padding: 8px 5px;
            font-size: 13px;
        }
    }

    /* Column search inputs */
    .column-search {
        width: 100%;
        margin-top: 5px;
        padding: 3px;
        font-size: 12px;
        border: 1px solid #ddd;
        border-radius: 3px;
    }

    /* DataTables responsive adjustments */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin: 10px 0;
    }

    @media (max-width: 768px) {
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            float: none;
            text-align: center;
        }

        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            float: none;
            text-align: center;
        }
    }
</style>

</head>
<body>

{# Generic macro to render any table cell with automatic link detection and popup support #}
{%- macro render_cell(field, cell_data, domain_row) -%}
<td>
{%- if cell_data -%}
{%- set value = cell_data.value or '' -%}
{%- set popup = cell_data.popup -%}
{%- set title_attr = ' title="' + popup + '"' if popup else '' -%}
{%- if field == 'domain' -%}
{%- set https_cell = domain_row.get('https') -%}
{%- set protocol = 'https' if https_cell and https_cell.value == 'yes' else 'http' -%}
<a href="{{ protocol }}://{{ value }}" target="_blank"{{ title_attr }}>{{ value }}</a>
{%- elif field == 'ip' and value and value not in ['(NXDOMAIN)', ''] and '(NoAnswer)' not in value -%}
<a href="https://ipinfo.io/{{ value }}" target="_blank"{{ title_attr }}>{{ value }}</a>
{%- elif field == 'redirect' and value and value not in ['(NXDOMAIN)', ''] -%}
<a href="https://{{ value }}" target="_blank"{{ title_attr }}>{{ value }}</a>
{%- elif value == 'yes' -%}
<span class="status-yes"{{ title_attr }}>{{ value }}</span>
{%- elif value == 'no' and field in ['our', 'our_redirect', 'https', 'header'] -%}
<span class="status-no"{{ title_attr }}>{{ value }}</span>
{%- elif value in ['', '(NXDOMAIN)'] or '(NoAnswer)' in value -%}
<span class="status-empty"{{ title_attr }}>{{ value }}</span>
{%- else -%}
<span{{ title_attr }}>{{ value }}</span>
{%- endif -%}
{%- endif -%}
</td>
{%- endmacro -%}

<div class="container">
    <h1>
        Domain Review Results
        <button class="header-toggle" onclick="togglePresetControls()">Show Controls</button>
    </h1>

    <!-- Preset Management Controls -->
    <div class="preset-controls" id="presetControls">
        <h3>Column Presets</h3>

        <div class="preset-group">
            <label>Load Preset:</label>
            <select id="presetSelect">
                <option value="">Select a preset...</option>
                <option value="default">Default View</option>
                <option value="minimal">Minimal View</option>
                <option value="full">Full View</option>
            </select>
            <button onclick="loadPreset()">Load</button>
        </div>

        <div class="preset-group">
            <label>Save Current View:</label>
            <input type="text" id="presetName" placeholder="Enter preset name...">
            <button onclick="savePreset()">Save</button>
        </div>

        <div class="preset-group">
            <label>Delete Preset:</label>
            <select id="deletePresetSelect">
                <option value="">Select preset to delete...</option>
            </select>
            <button class="delete" onclick="deletePreset()">Delete</button>
        </div>

        <div class="preset-group">
            <label>Reset All:</label>
            <button onclick="resetAll()" style="background: #6c757d;">Reset Filters & Columns</button>
        </div>
    </div>

    <div class="table-wrapper">
        <table id="domainsTable" class="display" style="width:100%">
        <thead>
            <tr>
                {%- for field in fieldnames %}
                <th data-column="{{ field }}">
                    <div class="header-content">
                        <span class="header-title">{{ field }}</span>
                    </div>
                </th>
                {%- endfor %}
            </tr>
        </thead>
        <tbody>
            {%- for domain_row in data %}
            <tr>
                {%- for field in fieldnames %}
                {%- set cell_data = domain_row.get(field) %}
                {{ render_cell(field, cell_data, domain_row) }}
                {%- endfor %}
            </tr>
            {%- endfor %}
        </tbody>
        </table>
    </div>
</div>

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.1.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/3.1.2/js/buttons.colVis.min.js"></script>
<script src="https://cdn.datatables.net/fixedheader/4.0.1/js/dataTables.fixedHeader.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#domainsTable').DataTable({
        // Basic configuration - No paging, scroll only
        paging: false,
        ordering: true,
        info: true,
        searching: true,
        responsive: true,

        // Layout configuration - No paging controls
        layout: {
            topStart: 'buttons',
            topEnd: 'search',
            bottomStart: 'info',
            bottomEnd: null
        },

        // Buttons configuration
        buttons: [
            {
                extend: 'colvis',
                text: 'Show/Hide Columns',
                columns: ':not(.noVis)',
                columnText: function(dt, idx, title) {
                    // Get the column title from the header
                    var columnTitle = $(dt.column(idx).header()).find('.header-title').text();
                    return columnTitle || title;
                }
            }
        ],

        // Column definitions
        columnDefs: [
            {
                targets: '_all',
                className: 'dt-body-left'
            }
        ],

        // Scrolling configuration - Fixed header with full height scroll
        scrollX: true,
        scrollY: 'calc(100vh - 200px)',
        scrollCollapse: true,
        fixedHeader: true,

        // Search configuration
        search: {
            regex: true,
            smart: false
        },

        // Initialize complete callback
        initComplete: function() {
            var api = this.api();

            // Add individual column search inputs
            api.columns().every(function() {
                var column = this;
                var header = $(column.header());
                var title = header.find('.header-title').text() || header.text();

                // Create search input
                var input = $('<input type="text" placeholder="Search ' + title + '" class="column-search">')
                    .appendTo(header)
                    .on('keyup change clear', function() {
                        if (column.search() !== this.value) {
                            column.search(this.value).draw();
                        }
                    });
            });
        }
    });

    // Initialize presets
    initializePresets();
    updatePresetSelects();
});

// Preset Management Functions
function initializePresets() {
    // Define default presets if they don't exist
    var defaultPresets = {
        'default': {
            name: 'Default View',
            columns: ['domain', 'ip', 'ipv6', 'our', 'redirect', 'https', 'cdn']
        },
        'minimal': {
            name: 'Minimal View',
            columns: ['domain', 'ip', 'our', 'https']
        },
        'full': {
            name: 'Full View',
            columns: {{ fieldnames | tojson | safe }}
        }
    };

    // Save default presets if not already saved
    for (var key in defaultPresets) {
        var storageKey = 'datatables_preset_' + key;
        if (!localStorage.getItem(storageKey)) {
            localStorage.setItem(storageKey, JSON.stringify(defaultPresets[key]));
        }
    }
}

function updatePresetSelects() {
    var presetSelect = $('#presetSelect');
    var deleteSelect = $('#deletePresetSelect');

    // Clear custom options (keep default ones)
    presetSelect.find('option').each(function() {
        if (!['', 'default', 'minimal', 'full'].includes($(this).val())) {
            $(this).remove();
        }
    });
    deleteSelect.empty().append('<option value="">Select preset to delete...</option>');

    // Add saved presets
    for (var i = 0; i < localStorage.length; i++) {
        var key = localStorage.key(i);
        if (key.startsWith('datatables_preset_')) {
            var presetKey = key.replace('datatables_preset_', '');
            var preset = JSON.parse(localStorage.getItem(key));

            // Add to load select if not a default preset
            if (!['default', 'minimal', 'full'].includes(presetKey)) {
                presetSelect.append('<option value="' + presetKey + '">' + preset.name + '</option>');
            }

            // Add to delete select
            deleteSelect.append('<option value="' + presetKey + '">' + preset.name + '</option>');
        }
    }
}

function savePreset() {
    var name = $('#presetName').val().trim();
    if (!name) {
        alert('Please enter a preset name');
        return;
    }

    var table = $('#domainsTable').DataTable();
    var visibleColumns = [];

    // Get currently visible columns
    table.columns().every(function(index) {
        if (this.visible()) {
            var header = $(this.header()).find('.header-title').text().trim();
            if (!header) {
                header = $(this.header()).text().trim();
            }
            visibleColumns.push(header);
        }
    });

    var preset = {
        name: name,
        columns: visibleColumns
    };

    // Save to localStorage
    var key = 'datatables_preset_' + name.toLowerCase().replace(/[^a-z0-9]/g, '_');
    localStorage.setItem(key, JSON.stringify(preset));

    // Update selects
    updatePresetSelects();

    // Clear input
    $('#presetName').val('');

    alert('Preset "' + name + '" saved successfully!');
}

function loadPreset() {
    var presetKey = $('#presetSelect').val();
    if (!presetKey) {
        alert('Please select a preset to load');
        return;
    }

    var storageKey = 'datatables_preset_' + presetKey;
    var preset = JSON.parse(localStorage.getItem(storageKey));

    if (!preset) {
        alert('Preset not found');
        return;
    }

    var table = $('#domainsTable').DataTable();

    // Hide all columns first
    table.columns().every(function() {
        this.visible(false);
    });

    // Show only columns in the preset
    preset.columns.forEach(function(columnName) {
        table.columns().every(function() {
            var header = $(this.header()).find('.header-title').text().trim();
            if (!header) {
                header = $(this.header()).text().trim();
            }
            if (header === columnName) {
                this.visible(true);
            }
        });
    });

    alert('Preset "' + preset.name + '" loaded successfully!');
}

function deletePreset() {
    var presetKey = $('#deletePresetSelect').val();
    if (!presetKey) {
        alert('Please select a preset to delete');
        return;
    }

    // Don't allow deletion of default presets
    if (['default', 'minimal', 'full'].includes(presetKey)) {
        alert('Cannot delete default presets');
        return;
    }

    if (!confirm('Are you sure you want to delete this preset?')) {
        return;
    }

    var storageKey = 'datatables_preset_' + presetKey;
    localStorage.removeItem(storageKey);

    updatePresetSelects();
    alert('Preset deleted successfully!');
}

function resetAll() {
    var table = $('#domainsTable').DataTable();

    // Clear all searches
    table.search('').draw();

    // Clear individual column searches
    table.columns().every(function() {
        this.search('');
        // Clear the search input in the header
        var header = $(this.header());
        header.find('input').val('');
    });

    // Show all columns (reset to default visibility)
    table.columns().every(function() {
        this.visible(true);
    });

    // Redraw the table
    table.draw();

    alert('All filters and column visibility reset to default!');
}

function togglePresetControls() {
    var controls = $('#presetControls');
    var button = $('.header-toggle');

    if (controls.hasClass('show')) {
        controls.removeClass('show');
        button.text('Show Controls');
    } else {
        controls.addClass('show');
        button.text('Hide Controls');
    }
}
</script>

</body>
</html>