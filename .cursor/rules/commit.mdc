---
description: MUST READ THESE RULES FOR GIT COMMIT BEFORE EVERY COMMIT
globs: 
alwaysApply: false
---
# 🚨 MANDATORY: Git Commit Rules

## 📋 KONTROLNÍ SEZNAM PŘED COMMITEM

### ✅ POVINNÉ KROKY
1. **Aktualizuj [TODO.md](mdc:.ai/TODO.md)** - označ dokončené úkoly jako hotové
2. **<PERSON><PERSON><PERSON><PERSON> dokončený úkol** nesmí zůstat označen jako otevřený
3. **Spus<PERSON> `mise run all`** a oprav všechny chyby a varování
4. **Znovu spusť testy** dokud všechny neprojdou
5. **Ov<PERSON><PERSON> soulad** s požadavky z [PRD.md](mdc:.ai/PRD.md)

### ✅ KONTROLNÍ SEZNAM
- [ ] Aktualizoval jsem TODO.md?
- [ ] Všechny dokončené úkoly jsou označeny jako hotové?
- [ ] <PERSON><PERSON><PERSON> jsem `mise run all`?
- [ ] Všechny testy prošly?
- [ ] Opravil jsem všechny chyby a varování?
- [ ] Implementace odpovídá specifikacím v PRD.md?

## 💻 COMMIT PŘÍKAZ

Commituj všechny změny v angličtině:
```bash
git add . ; git commit -m "Short summary (max 100 chars)" -m "Detailed description of changes"
```

## 🔗 SOUVISEJÍCÍ PRAVIDLA

Toto pravidlo je součástí workflow definovaného v [todo-prd-rules.mdc](mdc:todo-prd-rules.mdc).

