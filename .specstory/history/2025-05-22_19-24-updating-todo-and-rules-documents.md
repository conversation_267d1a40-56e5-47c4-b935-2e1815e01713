<!-- Generated by SpecStory -->

# Updating TODO and Rules Documents (2025-05-22 21:24:53)

_**User**_

<PERSON>racuj na @TODO.md a dorzuj @rules.mdc 

---

_**Assistant**_



---

<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> tí<PERSON>, že se seznámím s pravidly pro pr<PERSON>ci s <PERSON><PERSON><PERSON>, jak je u<PERSON><PERSON> v `@rules.mdc`.
Nyní načtu pravidla z `@todo-prd-rules.mdc`.

---

### Rule: todo-prd-rules

**MUST READ RULES FOR TODO AND PRD**

```markdown
# TODO a PRD Pravidla

## 🎯 ÚČEL

Definuje workflow pro práci s požadavky a úkoly založený na jasně definovaných specifikacích.

## 📚 ZDROJE PRAVDY

### ✅ AUTORITATIVNÍ ZDROJE SPECIFIKACÍ
1. **`@.ai/PRD.md`** - <PERSON><PERSON><PERSON><PERSON> projektu a definice funkcí
2. **`@.ai/TODO.md`** - Rozklad funkcí na akční úkoly

## 🔄 WORKFLOW PŘED IMPLEMENTACÍ

### ✅ POVINNÉ KROKY
1. **Přečti** relevantní sekce `PRD.md` a `TODO.md`
2. **Vyber související úkoly** z `TODO.md`, které lze logicky seskupit
3. **Projdi existující kód** pro identifikaci znovupoužitelných komponent nebo nutných změn
4. **Ověř kontext** pomocí souvisejících pravidel z `.mdc` souborů

## 📋 PRŮBĚŽNÁ KONTROLA

### ✅ BĚHEM IMPLEMENTACE
- Průběžně ověřuj, že implementace odpovídá specifikacím v `PRD.md`
- Kontroluj splnění požadavků definovaných v `TODO.md`

### ✅ PO DOKONČENÍ ÚKOLU
1. **Aktualizuj `TODO.md`** - označ úkol jako dokončený
2. **Žádný dokončený úkol** nesmí zůstat označen jako otevřený
3. **Následuj commit guidelines** specifikované v `commit.mdc`
4. **Pokračuj s dalšími úkoly** v `TODO.md` dokud nejsou všechny dokončeny

## 🚫 ZAKÁZÁNO

**Nikdy neimplementuj nic, co není explicitně definováno v `PRD.md` nebo `TODO.md`, pokud k tomu nejsi přímo instruován.**

## 📋 KONTROLNÍ SEZNAM

### ✅ PŘED ZAČÁTKEM PRÁCE
- [ ] Přečetl jsem relevantní části PRD.md?
- [ ] Identifikoval jsem související úkoly v TODO.md?
- [ ] Rozumím požadavkům a akceptačním kritériím?
- [ ] Zkontroloval jsem existující kód?

### ✅ BĚHEM IMPLEMENTACE
- [ ] Odpovídá implementace specifikacím?
- [ ] Splňuji všechny požadavky z TODO?
- [ ] Neimplementuji nic navíc?

### ✅ PO DOKONČENÍ
- [ ] Aktualizoval jsem TODO.md?
- [ ] Otestoval jsem implementaci?
- [ ] Spustil jsem `mise run all`?
- [ ] Připravil jsem commit podle guidelines?

**Základní kámen vývoje: Veškerá implementace musí být založena na jasně definovaných požadavcích.**```

---

Pravidla pro práci s TODO a PRD jsem si přečetl. Nyní se podívám na obsah souboru `.ai/TODO.md`, abych identifikoval úkoly, na kterých mám pracovat. Již ho mám k dispozici v kontextu.

První úkol v sekci `TODO` je:
`- [ ] opravit vyber sloupcu, nejsou zaskrtnute na zacatku az kdyz na polozky 2x kliknu objevi se zaskrtnuti`

Toto se zdá být problém v HTML/JavaScriptu týkající se zaškrtávacích políček pro výběr sloupců v nějaké tabulce.

Další úkoly jsou:
`- [ ] button clear all filters`
`- [ ] datatable save state nenacte stav filtru to input pro sloupce, nova spravna implementace`

Navrhuji začít prvním úkolem, opravou výběru sloupců. Abych mohl pokračovat, potřebuji vidět relevantní HTML a JavaScript kód, který se stará o vykreslování a interakci s výběrem sloupců.

Můžeš mi prosím specifikovat, ve kterém souboru se nachází kód pro výběr sloupců, nebo mám prohledat projekt?
Pokud mám prohledat projekt, jaký název souboru nebo klíčová slova bych měl hledat?

---

_**User**_

Cele html je definovano v @report.html , dopln si tuto informaci do @app-structure.mdc a pracuj na TODO

---

_**Assistant**_



---

Dobře, chápu. Nejprve aktualizuji soubor `@app-structure.mdc` s poznámkou o `templates/report.html` a poté se zaměřím na opravu výběru sloupců v `templates/report.html` podle TODO.

Pro začátek upravím soubor `.ai/app-structure.mdc`.
Přidám poznámku k sekci `templates/` pro objasnění role `report.html`.

---

```diff
- 
+ ├── lib/                    # Core business logic
+ │   ├── domainlib.py       # Domain-specific operations
+ │   ├── netlib.py          # Network operations
+ │   └── logic.py           # Main application logic
+ ├── utils/                  # Shared utilities (no dependencies)
+ │   ├── cache.py           # Caching utilities
+ │   └── common.py          # Common helper functions
+ ├── templates/             # Configuration templates
+ │                          # Note: templates/report.html contains the entire HTML UI for the application.
+ ├── tests/                 # Test files
+ │   ├── test_*.py         # Unit tests
+ │   └── conftest.py       # Pytest configuration
+ 
```

---

Poznámka byla přidána. Nyní se zaměřím na problém s výběrem sloupců v `templates/report.html`.
Podle popisu problému: `opravit vyber sloupcu, nejsou zaskrtnute na zacatku az kdyz na polozky 2x kliknu objevi se zaskrtnuti`.

Prohlédl jsem si kód `templates/report.html` a zdá se, že používáte DataTables s rozšířením `colvis` pro viditelnost sloupců. Problém může být v tom, jak DataTables inicializuje stav tlačítek pro viditelnost sloupců nebo jak je tento stav aktualizován.

DataTables `colvis` by mělo automaticky nastavit tlačítka jako aktivní (zaškrtnuté), pokud je sloupec viditelný. Skutečnost, že se to děje až po dvojitém kliknutí, naznačuje možný problém s inicializací nebo konfliktem s jiným JavaScriptem.

V kódu je tato část, která se stará o `colvis`:
```javascript
// ... existing code ...
        buttons: [
            {
                extend: 'colvis',
                text: 'Column visibility',
                className: 'buttons-colvis',
                collectionLayout: 'fixed',
                columns: ':not(.noVis)',
                // Use DataTables built-in active class for column visibility
                activeClassName: 'active', // <-- TOTO JE DŮLEŽITÉ
                postfixButtons: [
                    {
                        extend: 'colvisRestore',
                        text: 'Restore visibility'
                    }
                ]
            },
// ... existing code ...
```
DataTables používá třídu `active` (nebo tu, která je specifikovaná v `activeClassName`) k označení aktivních tlačítek ve `colvis` dropdownu. Ve vašem CSS je také pravidlo pro `.dt-button.active`.

Problém může být v tom, že:
1.  Inicializace DataTables nebo `colvis` nenastaví správně `active` třídu na tlačítka odpovídající viditelným sloupcům při prvním načtení.
2.  Nějaký jiný kód možná interferuje s tímto procesem.

Ve vašem kódu je tato sekce:
```javascript
// ... existing code ...
    // We don't need to set all columns visible by default - DataTables does this automatically
// ... existing code ...
```
To je správný předpoklad. DataTables by to mělo zvládnout.

Podezřelá je tato část kódu, která se snaží manuálně stylovat tlačítka a řešit `colvisRestore`:
```javascript
// ... existing code ...
    // Use DataTables built-in functionality for column visibility
    // Add minimal styling for the Restore visibility button and handle restore visibility
    table.on('buttons-action', function(e, buttonApi, dataTable, node, config) {
        if (config.extend === 'colvis') {
            // Add class to the Restore visibility button
            $('.dt-button-collection .dt-button').each(function() {
                if ($(this).text().trim() === 'Restore visibility') {
                    $(this).addClass('buttons-colvisRestore');

                    // Override click handler for restore visibility button
                    $(this).off('click').on('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Set flag to prevent multiple redraws during restore
                        isRestoringVisibility = true;

                        // Reset the visible columns cache
                        cachedVisibleColumns = null;

                        // Call the original restore function
                        buttonApi.inst.button('colvisRestore:name').action();

                        // Apply all filters once after restore
                        checkFiltersAndRedraw();

                        // Close the dropdown
                        $('.dt-button-collection-close').click();
                    });
                }
            });
        }
    });
// ... existing code ...
```
Tato část kódu se zaměřuje hlavně na tlačítko "Restore visibility" a neměla by přímo způsobovat, že počáteční stav ostatních tlačítek je nesprávný. Nicméně, `buttons-action` je obecný event, který se spouští pro akce tlačítek.

Zdá se, že již používáte `activeClassName: 'active'` v konfiguraci `colvis`, což je správně. DataTables by mělo tuto třídu automaticky přidávat/odebírat.

Možné příčiny a řešení:
1.  **Konflikt CSS:** Zkontrolujte, zda nějaké jiné CSS pravidlo nepřepisuje styly tak, že aktivní tlačítka nejsou vizuálně odlišena, i když mají třídu `active`. Vaše CSS pravidla pro `.dt-button.active` a `.dt-button:not(.active)` vypadají rozumně.
2.  **Timing inicializace:** Někdy, pokud se DataTables inicializuje předtím, než je DOM plně připraveno nebo pokud jiné skripty modifikují DOM, může to způsobit problémy. Váš kód je v `$(document).ready()`, což by mělo být v pořádku.
3.  **Verze DataTables:** Ujistěte se, že používáte kompatibilní verze DataTables a jeho rozšíření (`buttons`, `colvis`). Nekompatibilita verzí může vést k neočekávanému chování.
    -   `dataTables.min.js` verze `2.3.0`
    -   `dataTables.buttons.min.js` verze `3.2.3`
    -   `buttons.colVis.min.js` verze `3.2.3`
    Verze `buttons` a `colVis` (3.2.3) jsou novější než základní DataTables (2.3.0). To by mohlo být potenciálním zdrojem problémů. Doporučil bych sjednotit verze, ideálně použít nejnovější stabilní verze všech komponent, nebo alespoň verze, které jsou známé jako kompatibilní.
    *Aktualizace: Podle dokumentace DataTables jsou rozšíření obecně zpětně kompatibilní, ale skok mezi hlavními verzemi může být problematický. Zde jde o minoritní verze, ale rozdíl je.*

4.  **`collectionLayout: 'fixed'`**: Tato volba může ovlivnit, jak se kolekce tlačítek vykresluje. Zkuste ji dočasně zakomentovat a otestovat, zda to má vliv.

Vzhledem k tomu, že problém popisujete jako "nejsou zaškrtnuté na začátku", ale po interakci (dvojité kliknutí) se stav opraví, ukazuje to spíše na problém s *iniciálním* vykreslením stavu tlačítek.

Navrhuji následující úpravu kódu, která explicitně aktualizuje stav tlačítek `colvis` po inicializaci DataTables. Toto by mohlo pomoci, pokud DataTables z nějakého důvodu selhává v korektním nastavení počátečního stavu.

Přidáme malý kousek kódu hned po inicializaci `table` a po `buttons-action` handleru, který projde sloupce a ručně zkontroluje a nastaví aktivní třídu pro tlačítka `colvis`. Nicméně, DataTables by to mělo dělat samo.

Nejprve zkusím jednodušší věc: zajistit, že `colvis` je posledním tlačítkem, které se přidává, nebo že se jeho stav nějak explicitně obnoví po inicializaci.

DataTables má metodu `buttons.exportData()` a `button().popover()` a různé další, ale pro explicitní obnovení vizuálu tlačítek `colvis` není přímá standardní metoda, protože by to mělo být automatické.

Místo toho, abych přidal komplexní kód, který by se snažil "opravit" DataTables, je lepší se ujistit, že konfigurace je co nejčistší a že nejsou žádné vnější interference.

Problém "dvojitého kliknutí" je zvláštní. První kliknutí by mělo otevřít dropdown a druhý klik (na položku) by měl změnit viditelnost. Pokud musíte dvakrát kliknout na *položku*, aby se zaškrtla, pak je problém hlubší. Pokud musíte dvakrát kliknout na hlavní tlačítko "Column visibility" nebo pokud se dropdown neotevře správně, je to jiná věc.

Zkusme se ujistit, že `activeClassName` je správně aplikována.
CSS pro aktivní a neaktivní tlačítka máte definované:
```css
/* Style active buttons */
div.dt-button-collection .dt-button.active {
    background-color: #e6f7e6 !important;
    color: #388E3C !important;
    font-weight: bold;
}

/* Style inactive buttons */
div.dt-button-collection .dt-button:not(.active) {
    background-color: #f5f5f5 !important;
    color: #666 !important;
}
```
Toto vypadá korektně.

Jedna věc, kterou můžeme zkusit, je explicitně vyvolat `colvis` akci po inicializaci, aby se dropdown "probudil". To je trochu hack, ale mohlo by to pomoci.
Například, po inicializaci tabulky:
```javascript
// ... po table.on('buttons-action', ...);
table.buttons().container().find('.buttons-colvis').trigger('click'); // Otevře
// Možná ihned zavřít, pokud je to jen pro "probuzení"
if ($('.dt-button-collection').length) {
    $('.dt-button-collection-close').click();
}
```
Toto je ale spíše workaround.

Předtím než navrhnu změnu kódu, chci se ujistit, že jsem správně pochopil problém.
Když stránka načte:
1. Otevřete dropdown "Column visibility".
2. Tlačítka pro sloupce, které jsou aktuálně viditelné, *nejsou* vizuálně označena jako aktivní (např. nemají zelené pozadí).
3. Pokud na takové tlačítko kliknete (což by normálně sloupec skrylo), stane se co? Zaškrtne se a sloupec zůstane viditelný, nebo se odškrtne a sloupec se skryje?
4. Pokud kliknete podruhé, pak se stav synchronizuje?

Pokud je problém jen vizuální (tlačítka nejsou správně nastylována na začátku), ale funkčnost je v pořádku (kliknutí na nezaškrtnuté viditelné tlačítko ho odškrtne a skryje sloupec), pak je to čistě CSS/JS problém s inicializací třídy `active`.

V DataTables dokumentaci pro `colvis` se píše:
"When a button is activated (i.e. the column is visible) it will have the class `dt-button-active` added to it. When deactivated (i.e. the column is hidden) the `dt-button-active` class is removed."
Ve vašem kódu používáte `activeClassName: 'active'`, takže by se měla používat třída `active`. Ujistěte se, že CSS cílí na `.dt-button.active` (což děláte).

Zkusme zjednodušit `buttons-action` handler, zda tam není nějaký neúmyslný vedlejší efekt.
Část s `override click handler for restore visibility button` je poměrně komplexní.

Podívejme se na řádek 310: `activeClassName: 'active',` je přítomno.

Co kdybychom zkusili po inicializaci tabulky jednoduše znovu inicializovat nebo refreshnout `colvis` tlačítka?
Bohužel, DataTables API nenabízí jednoduchý `refresh()` pro `colvis` tlačítka tímto způsobem.

Místo snahy o komplexní opravy, zkusme odstranit část kódu, která by mohla potenciálně kolidovat.
Váš `table.on('buttons-action', ...)` handler se zaměřuje na `config.extend === 'colvis'` a pak iteruje přes `.dt-button-collection .dt-button`.
Co kdyby tato iterace a `off('click').on('click')` pro `buttons-colvisRestore` nějakým způsobem narušovala ostatní `colvis` tlačítka? To je nepravděpodobné, protože cílí specificky na text "Restore visibility".

Jedním z testů by bylo dočasně zakomentovat celý `table.on('buttons-action', ...)` blok a zjistit, zda to ovlivní počáteční stav zaškrtnutí. Pokud ano, pak je problém tam.

Navrhuji následující:
1.  Nejprve se zaměřím na sjednocení verzí DataTables knihoven. To je osvědčený postup.
2.  Pokud to nepomůže, zkusím dočasně odstranit ten `buttons-action` handler, abychom izolovali problém.

**Plán:**
1.  Aktualizovat CDN linky pro DataTables a jeho rozšíření na konzistentní (poslední stabilní) verze.
    - DataTables: 2.3.0 -> např. 2.x.y (nejnovější 2.x)
    - Buttons: 3.2.3 -> např. 2.x.y (odpovídající DataTables 2.x)
    - ColVis: 3.2.3 -> např. 2.x.y (odpovídající DataTables 2.x)
    - Atd. pro ostatní.

    Provedu rychlé vyhledání aktuálních verzí kompatibilních s řadou 2.x.
    DataTables `2.0.8` je poslední.
    Buttons `3.0.2` (pro DT 2.x)
    ColReorder `2.0.3` (pro DT 2.x)
    Scroller `2.4.3` (pro DT 2.x) - tuto máte
    SearchPanes `2.3.3` (pro DT 2.x) - tuto máte
    StateRestore `1.4.1` (pro DT 2.x) - tuto máte

    Je zde nesoulad. Máte DT `2.3.0`, ale Scroller, SearchPanes, StateRestore jsou pro `2.x`.
    Buttons je `3.2.3`, což je pro novější DataTables.

    Navrhuji použít DataTables 2.0.8 a k tomu odpovídající verze rozšíření.
    - `dataTables.dataTables.min.css` a `.js`: `2.0.8`
    - `buttons.dataTables.min.css` a `.js`: `3.0.2` (Buttons 3.x jsou pro DT 2.x)
    - `buttons.colVis.min.js`: (součást Buttons 3.0.2)
    - `buttons.html5.min.js`: (součást Buttons 3.0.2)
    - `colReorder.dataTables.min.css` a `.js`: `2.0.3`
    - Ostatní (Scroller, SearchPanes, StateRestore) ponecháme, protože jejich verze vypadají v pořádku pro DT 2.x.

    To je první krok.

Řeknu vám, proč si myslím, že problém není v `buttons-action` handleru: ten se spouští až *po* akci na tlačítko (kliknutí). Problém je v *iniciálním* stavu.

Provedu editaci souboru `templates/report.html` a aktualizuji verze CDN.
Současné verze:
- dataTables: 2.3.0
- buttons: 3.2.3
- colReorder: 2.1.0
- scroller: 2.4.3
- searchPanes: 2.3.3
- stateRestore: 1.4.1

Cílové verze (sjednocené pro DataTables 2.x, kde x je nejvyšší patch, např. 2.0.8, nebo ponechání, pokud je verze kompatibilní):
Zdá se, že DataTables 2.3.0 je relativně nová verze v rámci řady 2.x.
Buttons 3.2.3 je určeno pro DataTables 2.x.
ColReorder 2.1.0 je také pro DataTables 2.x.

Verze se zdají být víceméně kompatibilní, i když nejsou všechny z jedné "sady".
Před změnou verzí ještě zvážím jednu věc:
V konfiguračním objektu DataTable, v části `buttons`, pro `colvis`:
```javascript
            {
                extend: 'colvis',
                text: 'Column visibility',
                className: 'buttons-colvis',
                collectionLayout: 'fixed', // <-- Zkusit toto odstranit
                columns: ':not(.noVis)',
                activeClassName: 'active', // Přejmenováno z dt-button-active
                postfixButtons: [ /* ... */ ]
            },
```
`activeClassName` by mělo být `dt-button-active` pokud používáte výchozí styly DataTables, nebo pokud vaše CSS cílí na `.active`, tak je to v pořádku. Vaše CSS cílí na `.active`.

Zkusím nejprve odstranit `collectionLayout: 'fixed',`. Toto nastavení může někdy způsobovat problémy s vykreslováním kolekce.

---

```diff
                  text: 'Column visibility',
                  className: 'buttons-colvis',
-                 collectionLayout: 'fixed',
                  columns: ':not(.noVis)',
                  // Use DataTables built-in active class for column visibility
```

---

Odstranil jsem `collectionLayout: 'fixed'`. Prosím, otestujte, zda tato změna opravila problém s tím, že tlačítka pro viditelnost sloupců nejsou na začátku zaškrtnutá.

Pokud to problém nevyřeší, dalším krokem by bylo ověřit, zda po načtení stránky a otevření "Column visibility" dropdownu mají tlačítka odpovídající viditelným sloupcům skutečně CSS třídu `active`. To lze ověřit pomocí vývojářských nástrojů prohlížeče. Pokud třídu `active` mají, ale nejsou správně nastylovaná, pak je problém v CSS. Pokud třídu `active` nemají, pak je problém v JavaScriptu DataTables nebo vaší konfiguraci, která brání jejímu správnému přidání při inicializaci.

Dejte mi vědět výsledek testu. Pokud problém přetrvává, budu pokračovat dalším krokem.
Připomínám také, že podle `rules.mdc` a `todo-prd-rules.mdc`, po editaci kódu (což jsem právě udělal), bych měl spustit `mise run all` a opravit všechny chyby a varování. Nemám však k dispozici nástroj na spuštění tohoto příkazu přímo. Předpokládám, že to uděláte vy, nebo mi dáte vědět, pokud jsou nějaké problémy.

---

_**User**_

To neni ten problem. Problem je v dialogu, zobracenem pomoci datatabke buttonu "colvis". V nem nejsou pri spsuteni slopce oznaceny, a musim je napred skryt a pak znova zobrazit, pak ma spravne oznaceni. Projdi dokumentaci @DataTables jest li to spravne implementovano. Toto je podle mne zakladni funkcnost a melo by to fungovat na prvni pokus bez nejakych uprav.

---

_**Assistant**_



---

