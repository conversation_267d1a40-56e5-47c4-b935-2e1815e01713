#!/usr/bin/env python3

import hashlib
import json
import re
import time
from datetime import datetime
from email.utils import parsedate_to_datetime
from pathlib import Path
from typing import Any, Dict, Optional

from out import log_debug

# Cache directory
CACHE_DIR: Path = Path(".cache")

# Global variables for cache configuration
CLI_CACHE_TIME_OVERRIDE: Optional[int] = None

# Global flag for disabling cache
DISABLE_CACHE: Optional[bool] = None


def ensure_cache_dir() -> None:
    """Ensures the cache directory exists."""
    if not DISABLE_CACHE:
        CACHE_DIR.mkdir(exist_ok=True)


def init_cache(disable: bool = False, cache_time_override: Optional[int] = None) -> None:
    """Initializes the cache system."""

    global DISABLE_CACHE  # pylint: disable=global-statement
    DISABLE_CACHE = disable

    global CLI_CACHE_TIME_OVERRIDE  # pylint: disable=global-statement
    CLI_CACHE_TIME_OVERRIDE = cache_time_override

    if not DISABLE_CACHE:
        ensure_cache_dir()


def get_cache_path(cache_type: str, key: str) -> Path:
    """Gets the cache file path for a given key."""
    # Use hashlib to create a safe filename from the key
    hashed_key = hashlib.md5(key.encode()).hexdigest()
    return CACHE_DIR / f"{cache_type}_{hashed_key}.json"


def load_from_cache(cache_type: str, key: str) -> Optional[Dict[str, Any]]:
    """Loads data from cache if available and not expired."""
    # Skip cache if disabled globally or via parameter (for backward compatibility)
    if DISABLE_CACHE:
        return None

    if DISABLE_CACHE is None:
        init_cache()

    cache_path = get_cache_path(cache_type, key)

    if not cache_path.exists():
        return None

    try:
        with open(cache_path, "r") as f:
            cache_data = json.load(f)

        # Verify cache integrity with key
        stored_key = cache_data.get("key", None)
        if stored_key != key:
            log_debug(f"Cache key mismatch for {key}. Expected {key}, got {stored_key}")
            return None

        # Check if cache is expired
        cache_timestamp = cache_data.get("timestamp", 0)
        # Log the received cache_time (CLI override) before using it
        log_debug(f"load_from_cache for {key}: global CLI_CACHE_TIME_OVERRIDE = {CLI_CACHE_TIME_OVERRIDE}")
        # If CLI_CACHE_TIME_OVERRIDE (global) is provided, it dictates the expiration regardless of stored expiration
        effective_expiration_time = CLI_CACHE_TIME_OVERRIDE if CLI_CACHE_TIME_OVERRIDE is not None else cache_data.get("expiration", 3600)

        if time.time() - cache_timestamp > effective_expiration_time:
            log_debug(f"Cache expired for {key}. Age: {time.time() - cache_timestamp}s, Max age: {effective_expiration_time}s")
            return None

        data = cache_data.get("data")
        if isinstance(data, dict):
            return data
        return None
    except (json.JSONDecodeError, KeyError):
        # Invalid cache file
        return None


def save_to_cache(cache_type: str, key: str, data: Dict[str, Any], expiration: int = 3600) -> Optional[bool]:
    """Saves data to cache with expiration time."""
    # Skip cache if disabled globally or via parameter (for backward compatibility)
    if DISABLE_CACHE:
        return None

    if DISABLE_CACHE is None:
        init_cache()

    cache_path: Path = get_cache_path(cache_type, key)

    # If CLI_CACHE_TIME_OVERRIDE (global) is provided, it dictates the expiration for this save
    effective_expiration: int = CLI_CACHE_TIME_OVERRIDE if CLI_CACHE_TIME_OVERRIDE is not None else expiration

    cache_data: Dict[str, Any] = {
        "timestamp": time.time(),
        "expiration": effective_expiration,  # Store the actual expiration time used
        "key": key,
        "data": data,
    }

    try:
        with open(cache_path, "w") as f:
            json.dump(cache_data, f)
        return True
    except IOError:
        log_debug(f"Failed to write to cache file {cache_path}")
        # Silently fail for now if cannot write to cache, return False
    return False


def get_expiration_from_headers(response_headers: Dict[str, str]) -> int:
    """Extract cache expiration time from HTTP headers."""
    expiration = 3600  # Default 1 hour
    if "Cache-Control" in response_headers:
        cc = response_headers["Cache-Control"]
        max_age_match = re.search(r"max-age=(\d+)", cc, re.IGNORECASE)
        if max_age_match:
            expiration = int(max_age_match.group(1))
    elif "Expires" in response_headers:
        try:
            # Ensure datetime.now() is timezone-aware if 'Expires' is, or make 'Expires' naive
            # For simplicity, assuming parsedate_to_datetime handles this well enough for delta.
            expires_dt = parsedate_to_datetime(response_headers["Expires"])
            if expires_dt.tzinfo is None:
                now_dt = datetime.now()
            else:
                now_dt = datetime.now(expires_dt.tzinfo)
            expiration = max(0, int((expires_dt - now_dt).total_seconds()))
        except Exception as e_parse:
            log_debug(f"Could not parse 'Expires' header: {e_parse}")

    return expiration
