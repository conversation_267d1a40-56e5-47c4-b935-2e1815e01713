[tools]
python = "3.12"
uv = "latest"

[env]
_.python.venv = { path = ".venv", create = true }

[tasks.install]
run = "uv pip install -r pyproject.toml --all-extras"

[tasks.reinstall_venv]
description = "Reinstall the virtual environment"
run = """
#!/bin/bash -x
rm -rf .venv
mise i -q
"""
depends_post = "install"


[tasks.format]
description = "Spustí isort a black pro automatické formátování kódu."
run = [
    "isort *.py",
    "black *.py"
]

[tasks.lint]
description = "Spustí pylint pro kontrolu kódu."
run = "pylint --output-format=colorized *.py"
wait_for = [ "format" ]

[tasks.typecheck]
description = "Spustí mypy pro kontrolu typů."
run = "mypy --show-error-code-links --pretty ."
wait_for = [ "format" ]

[tasks.test]
description = "Spustí unittesty."
run = "pytest"
wait_for = [ "lint" ]

[tasks.testpy]
description = "Spustí unittesty s parametrem"
run = "pytest"

[tasks.all]
description = "Spustí všechny kontroly."
depends = ["format", "lint", "typecheck", "test"]


[tasks.run]
run = "python3 nginx_domain_review.py"


[tasks.test_csv]
# progress not work in mise due to output is redirected to mise.
run = "python3 nginx_domain_review.py @test.args"

[tasks.test_html]
# progress not work in mise due to output is redirected to mise.
run = "python3 nginx_domain_review.py @test.args --html > test.out.html"

[tasks.httpserver]
run = "npx http-server"
# direct stdio - divne
# raw = true

[tasks.mcp-playwright]
run = "npx @playwright/mcp@latest --port 8931"


[tasks.c]
run = "python3 nginx_domain_review.py @c.args"

[tasks.c-html]
# progress not work in mise due to output is redirected to mise.
run = "python3 nginx_domain_review.py @c.args --html > c.out.html"

[tasks.c-html-10]
# progress not work in mise due to output is redirected to mise.
run = "python3 nginx_domain_review.py @c.args --html --debug --max-domains 10 > c.out.html"

[tasks.c-debug]
run = "python3 nginx_domain_review.py @c.args --debug --html > c.out.html"

[tasks.c-html-open]
run = "xdg-open c.out.html"


[tasks.w-html]
# progress not work in mise due to output is redirected to mise.
run = "python3 nginx_domain_review.py @w.args --html > w.out.html"


