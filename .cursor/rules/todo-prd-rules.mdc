---
description: MUST READ RULES FOR TODO AND PRD
globs: 
alwaysApply: false
---
# TODO a PRD Pravidla

## 🎯 ÚČEL

Definuje workflow pro práci s požadavky a úkoly založený na jasně definovaných specifikacích.

## 📚 ZDROJE PRAVDY

### ✅ AUTORITATIVNÍ ZDROJE SPECIFIKACÍ
1. **`@.ai/PRD.md`** - P<PERSON>ehled projektu a definice funkcí
2. **`@.ai/TODO.md`** - Rozklad funkcí na akční úkoly

## 🔄 WORKFLOW PŘED IMPLEMENTACÍ

### ✅ POVINNÉ KROKY
1. **Přečti** relevantní sekce `PRD.md` a `TODO.md`
2. **Vyber související úkoly** z `TODO.md`, které lze logicky seskupit
3. **Projdi existující kód** pro identifikaci znovupoužitelných komponent nebo nutných změn
4. **O<PERSON><PERSON><PERSON> kontext** pomocí souvisejících pravidel z `.mdc` souborů

## 📋 PRŮBĚŽNÁ KONTROLA

### ✅ BĚHEM IMPLEMENTACE
- Průběžně ověřuj, že implementace odpovídá specifikacím v `PRD.md`
- Kontroluj splnění požadavků definovaných v `TODO.md`

### ✅ PO DOKONČENÍ ÚKOLU
1. **Aktualizuj `TODO.md`** - označ úkol jako dokončený
2. **Žádný dokončený úkol** nesmí zůstat označen jako otevřený
3. **Následuj commit guidelines** specifikované v `commit.mdc`
4. **Pokračuj s dalšími úkoly** v `TODO.md` dokud nejsou všechny dokončeny

## 🚫 ZAKÁZÁNO

**Nikdy neimplementuj nic, co není explicitně definováno v `PRD.md` nebo `TODO.md`, pokud k tomu nejsi přímo instruován.**

## 📋 KONTROLNÍ SEZNAM

### ✅ PŘED ZAČÁTKEM PRÁCE
- [ ] Přečetl jsem relevantní části PRD.md?
- [ ] Identifikoval jsem související úkoly v TODO.md?
- [ ] Rozumím požadavkům a akceptačním kritériím?
- [ ] Zkontroloval jsem existující kód?

### ✅ BĚHEM IMPLEMENTACE
- [ ] Odpovídá implementace specifikacím?
- [ ] Splňuji všechny požadavky z TODO?
- [ ] Neimplementuji nic navíc?

### ✅ PO DOKONČENÍ
- [ ] Aktualizoval jsem TODO.md?
- [ ] Otestoval jsem implementaci?
- [ ] Spustil jsem `mise run all`?
- [ ] Připravil jsem commit podle guidelines?

**Základní kámen vývoje: Veškerá implementace musí být založena na jasně definovaných požadavcích.**
