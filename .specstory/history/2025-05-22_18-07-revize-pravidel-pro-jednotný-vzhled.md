<!-- Generated by SpecStory -->

# Revize pravidel pro jednotný vzhled (2025-05-22 20:07:45)

_**User**_

/Generate Cursor Rules podivej se na vsechnapravidla a udelej revizi. Potrebuji aby mela jednotny vzhled a byla formalne spravna. Cil je aby jsi pracoval profesinalne a udrzitelne. Navrhni zmeny. Ptej se pokud je vhodne neco dalsiho dolnit nebo predelat.

---

_**Assistant**_

I'll help you review and revise all the Cursor rules to ensure they have a consistent appearance and are formally correct. Let me first examine the existing rules in your workspace.

---

<details>
            <summary>Listed directory .windsurf/rules • **5** results</summary>
        
| Name |
|-------|
| 📄 `python-cli.md` |
| 📄 `rules.md` |
| 📄 `todo-prd-rules.md` |
| 📄 `main.md` |
| 📄 `mdc.md` |

</details>

---

Now let me read each rule file to understand their current structure and content:

---

Read file: .windsurf/rules/main.md

---

Read file: .windsurf/rules/rules.md

---

Read file: .windsurf/rules/todo-prd-rules.md

---

Read file: .windsurf/rules/python-cli.md

---

Read file: .windsurf/rules/mdc.md

---

Now I'll check if there's a `.cursor/rules` directory as well, since the current rules are in `.windsurf/rules`:

---

<details>
            <summary>Listed directory .cursor • **1** results</summary>
        
| Name |
|-------|
| 📁 `rules` |

</details>

---

<details>
            <summary>Listed directory .cursor/rules • **7** results</summary>
        
| Name |
|-------|
| 📄 `circular-imports.mdc` |
| 📄 `python-code-quality.mdc` |
| 📄 `rules.mdc` |
| 📄 `todo-prd-rules.mdc` |
| 📄 `main.mdc` |
| 📄 `python-cli.mdc` |
| 📄 `mdc.mdc` |

</details>

---

Perfect! I can see there are rules in both `.windsurf/rules` (with `.md` extension) and `.cursor/rules` (with `.mdc` extension). Let me read a couple of the `.mdc` files to see their format:

---

Read file: .cursor/rules/main.mdc

---

Read file: .cursor/rules/rules.mdc

---

Now let me check one more file to understand the additional rules:

---

Read file: .cursor/rules/circular-imports.mdc

---

Based on my review of all the rules, I can see several issues that need to be addressed for consistency and formal correctness. Here's my analysis and recommendations:

## 🔍 **ANALÝZA SOUČASNÉHO STAVU**

### **Problémy identifikované:**

1. **Nekonzistentní umístění**: Pravidla jsou v `.windsurf/rules/` (s `.md`) i `.cursor/rules/` (s `.mdc`)
2. **Nekonzistentní formát**: Některé soubory mají YAML metadata, jiné ne
3. **Nekonzistentní přípony**: `.md` vs `.mdc`
4. **Chybějící `<rule>` tagy**: Některé soubory je mají, jiné ne
5. **Překlep**: "editiong" místo "editing" v `rules.mdc`
6. **Nekonzistentní struktura**: Různé styly nadpisů a formátování

### **Doporučené změny:**

## 📋 **NÁVRH REVIZE**

### **1. Standardizace umístění a formátu**
- **Použít pouze** `.cursor/rules/` s příponou `.mdc`
- **Smazat** duplicitní soubory z `.windsurf/rules/`
- **Jednotný formát** bez YAML metadata (podle Cursor dokumentace)

### **2. Standardizace struktury**
```markdown
# Název Pravidla

## 🎯 ÚČEL
Stručný popis účelu pravidla

## ✅ PRAVIDLA
1. Konkrétní pravidlo
2. Další pravidlo

## 🚫 ZAKÁZÁNO
- Co nedělat

## 📋 KONTROLNÍ SEZNAM
- [ ] Kontrolní bod 1
- [ ] Kontrolní bod 2
```

### **3. Oprava chyb**
- Opravit překlep "editiong" → "editing"
- Přidat chybějící `<rule>` tagy kde potřeba
- Sjednotit formátování

**Chcete, abych provedl tyto změny? Konkrétně:**

1. **Smazat** duplicitní soubory z `.windsurf/rules/`?
2. **Opravit** všechny identifikované chyby?
3. **Standardizovat** formát všech pravidel?
4. **Přidat** nějaká další pravidla pro lepší pokrytí projektu?

**Nebo máte jiné priority či specifické požadavky na formát?**

---

_**User**_

Ano uprav to. Tag <rule> nikde nedelej a cele mdc.mdc smaz, to se mi zda nesmyslne. Hlavne potrebuji pravidlo o strukture aplikace a o sposuteni a dodrzovani testu lint ...

---

_**Assistant**_



---



---



---

