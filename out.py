#!/usr/bin/env python3

import sys
from typing import Any, NoReturn, Optional

from tqdm import tqdm

# Global flags for debug and progress mode
DEBUG_MODE: bool = False
PROGRESS_ACTIVE_MODE: bool = False
IS_ERROR: bool = False

# Global progress bar instance
progress_bar: Optional[Any] = None  # pylint: disable=invalid-name disable=unsubscriptable-object


def end() -> NoReturn:
    """Ends the program with appropriate exit code."""

    if IS_ERROR:
        sys.exit(1)
    sys.exit(0)


def log_debug(message: str) -> None:
    """Logs a message to stderr if DEBUG_MODE is True, using progress bar's write method if active."""

    if DEBUG_MODE:
        if PROGRESS_ACTIVE_MODE and progress_bar is not None:
            progress_bar.write(message, file=sys.stderr)
        else:
            print("DEBUG: " + message, file=sys.stderr)


def log_error(message: str) -> None:
    """Logs a error message to stderr."""

    global IS_ERROR  # pylint: disable=global-statement
    IS_ERROR = True

    if PROGRESS_ACTIVE_MODE and progress_bar is not None:
        progress_bar.write(message, file=sys.stderr)
    else:
        print("DEBUG: " + message, file=sys.stderr)


def init_progress_bar(total: int, description: str = "Processing", unit: str = "item") -> None:
    """Initialize a progress bar.

    Args:
        total: Total number of items to process
        description: Description text for the progress bar
        unit: Unit name for the items being processed

    Returns:
        None
    """
    global progress_bar  # pylint: disable=global-statement

    if PROGRESS_ACTIVE_MODE:
        progress_bar = tqdm(total=total, desc=description, unit=unit)


def update_progress(n: int = 1) -> None:
    """Update the progress bar by n steps."""

    if PROGRESS_ACTIVE_MODE and progress_bar is not None:
        progress_bar.update(n)


def close_progress() -> None:
    """Close the progress bar."""
    global progress_bar  # pylint: disable=global-statement

    if PROGRESS_ACTIVE_MODE and progress_bar is not None:
        progress_bar.close()
        progress_bar = None
