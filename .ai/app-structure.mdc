---
description:
globs:
alwaysApply: false
---
├── lib/                    # Core business logic
│   ├── domainlib.py       # Domain-specific operations
│   ├── netlib.py          # Network operations
│   └── logic.py           # Main application logic
├── utils/                  # Shared utilities (no dependencies)
│   ├── cache.py           # Caching utilities
│   └── common.py          # Common helper functions
├── templates/             # Configuration templates
│                          # Note: templates/report.html contains the entire HTML UI for the application.
├── tests/                 # Test files
│   ├── test_*.py         # Unit tests
│   └── conftest.py       # Pytest configuration
