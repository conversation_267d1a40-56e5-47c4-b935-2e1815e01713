#!/usr/bin/env python3

import argparse
import csv
import re
import sys
from typing import Dict, List, Set, Tuple

from jinja2 import Environment, FileSystemLoader

import out
from cache import init_cache
from domainlib import reverse_domain
from logic import ALL_COLUMNS, DEFAULT_COLUMNS, LogicContext, TableData, create_domain_data
from out import close_progress, init_progress_bar, update_progress

# List of our IP addresses (modify as needed)
OUR_IPS: Set[str] = {"127.0.0.1"}
OUR_IPV6S: Set[str] = set()  # Default is empty, to be populated via CLI args or file
DOMAINS: Set[str] = set()  # Set of all domains in the input


class NginxLogicContext(LogicContext):
    """Implementation of LogicContext for nginx_domain_review.py that uses global sets directly."""

    # Není potřeba přepisovat __init__, protože pouze deleguje na rodiče
    # a nepřidává žádnou da<PERSON><PERSON>

    def check_in_our_ips(self, ip: str) -> bool:
        """Check if an IP is in our IPs set.

        Args:
            ip: The IP address to check

        Returns:
            bool: True if the IP is in our IPs set, False otherwise
        """
        # Přímý přístup ke globální proměnné
        return ip in OUR_IPS

    def check_in_our_ipv6s(self, ipv6: str) -> bool:
        """Check if an IPv6 is in our IPv6s set.

        Args:
            ipv6: The IPv6 address to check

        Returns:
            bool: True if the IPv6 is in our IPv6s set, False otherwise
        """
        # Přímý přístup ke globální proměnné
        return ipv6 in OUR_IPV6S

    def check_in_our_domains(self, domain: str) -> bool:
        """Check if a domain is in our domains set.

        Args:
            domain: The domain to check

        Returns:
            bool: True if the domain is in our domains set, False otherwise
        """
        # Přímá kontrola, zda je doména v seznamu
        if domain in DOMAINS:
            return True

        # Kontrola wildcard domén - pokud je v seznamu *.example.com a hledáme www.example.com
        parts = domain.split(".", 1)
        if len(parts) > 1:
            wildcard_domain = "*." + parts[1]  # Převod libovolné subdomény na *.example.com
            if wildcard_domain in DOMAINS:
                return True

        return False


def extract_server_blocks(config: str) -> List[str]:
    """Extracts server blocks from NGINX configuration."""
    server_blocks: List[str] = []
    lines = config.splitlines()
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        if line.startswith("server {"):
            block = []
            depth = 1
            i += 1
            while i < len(lines) and depth > 0:
                line = lines[i].strip()
                block.append(line)
                if "{" in line:
                    depth += line.count("{")
                if "}" in line:
                    depth -= line.count("}")
                i += 1
            server_blocks.append("\n".join(block))
        else:
            i += 1
    return server_blocks


def extract_domains(config: str) -> Set[str]:
    """Extracts all domains from NGINX configuration, returns deduplicated list."""
    domains: Set[str] = set()
    server_blocks = extract_server_blocks(config)

    for block in server_blocks:
        # Use findall with DOTALL flag to handle multiline server_name directives
        matches = re.findall(r"server_name\s+(.+?);", block, re.DOTALL)
        for match in matches:
            # Split by whitespace and filter out empty strings
            names = match.split()
            for name in names:
                # Skip '_' as it's a special nginx notation for default server
                if name != "_":
                    domains.add(name)

    return domains


def read_domains_from_list(file_path: str) -> Set[str]:
    """Reads domains from a plain text file, one per line.

    If file_path is "-", reads from stdin instead.
    """
    domains: Set[str] = set()

    # If file_path is "-", read from stdin
    if file_path == "-":
        for line in sys.stdin:
            # Remove comments (anything starting with #)
            line = line.split("#", 1)[0].strip()
            # Skip empty lines
            if not line:
                continue
            # Add the domain to the set
            domains.add(line)
    else:
        # Read from file
        with open(file_path, "r") as f:
            for line in f:
                # Remove comments (anything starting with #)
                line = line.split("#", 1)[0].strip()
                # Skip empty lines
                if not line:
                    continue
                # Add the domain to the set
                domains.add(line)
    return domains


def read_ips_from_file(file_path: str) -> Set[str]:
    """Reads IP addresses from a file, one per line. Lines starting with # are comments.
    Comments can also appear after the IP address.

    If file_path is "-", reads from stdin instead.
    """
    ips: Set[str] = set()

    # If file_path is "-", read from stdin
    if file_path == "-":
        for line in sys.stdin:
            # Remove comments (anything starting with #)
            line = line.split("#", 1)[0].strip()
            # Skip empty lines
            if not line:
                continue
            # Add the IP to the set
            ips.add(line)
    else:
        # Read from file
        with open(file_path, "r") as f:
            for line in f:
                # Remove comments (anything starting with #)
                line = line.split("#", 1)[0].strip()
                # Skip empty lines
                if not line:
                    continue
                # Add the IP to the set
                ips.add(line)
    return ips


def convert_tabledata_to_str(data: List[Dict[str, TableData]]) -> List[Dict[str, str]]:
    """Converts a list of dictionaries with TableData objects to a list of dictionaries with strings.

    Args:
        data: List of dictionaries with TableData objects

    Returns:
        List of dictionaries with string values
    """
    result: List[Dict[str, str]] = []
    for row in data:
        str_row: Dict[str, str] = {}
        for key, value in row.items():
            str_row[key] = str(value)
        result.append(str_row)
    return result


def output_html(data: List[Dict[str, TableData]], fieldnames: List[str]) -> str:
    """Outputs data as an HTML table with clickable domain links using Jinja2 templates."""
    # Setup Jinja2 environment
    env = Environment(loader=FileSystemLoader("templates"))

    # Load template and render with data
    template = env.get_template("report.html")
    return template.render(data=data, fieldnames=fieldnames)


def main() -> None:
    """Main function of the script."""
    parser = argparse.ArgumentParser(description="NGINX configuration parser", fromfile_prefix_chars="@")
    parser.add_argument(
        "config_files",
        nargs="*",
        help="Path to the input file(s) (nginx config or domain list) or domain names when --domains is used. Use '-' to read from stdin.",
    )
    parser.add_argument(
        "--format",
        choices=["nginx", "list"],
        default="nginx",
        help="Input format: 'nginx' for nginx -T output, 'list' for plain list of domains",
    )
    parser.add_argument("--header", help="Header name for detection")
    parser.add_argument("--header-re", help="Regular expression for header value")
    parser.add_argument("--add-headers", help="Comma-separated list of HTTP response headers to add as columns")
    parser.add_argument("--max-domains", type=int, help="Maximum number of domains to process")
    parser.add_argument("--our-ips", help="Comma-separated list of our IPv4 addresses")
    parser.add_argument("--our-ips-file", help="File containing our IPv4 addresses, one per line. Use '-' to read from stdin.")
    parser.add_argument("--our-ipv6s", help="Comma-separated list of our IPv6 addresses")
    parser.add_argument("--our-ipv6s-file", help="File containing our IPv6 addresses, one per line. Use '-' to read from stdin.")
    parser.add_argument(
        "--columns",
        help=f"Comma-separated list of columns to display in output. Available: {','.join(ALL_COLUMNS)}",
    )
    parser.add_argument(
        "--html",
        action="store_true",
        help="Output as HTML table with clickable domain links",
    )
    parser.add_argument("--cache-time", type=int, help="Override cache expiration time in seconds")
    parser.add_argument(
        "--wait-time",
        type=float,
        default=1.0,
        help="Time to wait between HTTP requests in seconds (default: 1.0)",
    )
    parser.add_argument("--progress", action="store_true", help="Show progress bar during processing")
    parser.add_argument(
        "--disable-cache",
        action="store_true",
        help="Disable caching for all operations",
    )
    parser.add_argument(
        "--start-protocol",
        choices=["http", "https"],
        default="http",
        help="Protocol to start with for all requests (default: http)",
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug output for requests and responses",
    )
    parser.add_argument(
        "--domains",
        action="store_true",
        help="Treat positional arguments as domain names instead of config files",
    )

    args = parser.parse_args()

    # Set global debug and progress flags
    out.DEBUG_MODE = args.debug
    out.PROGRESS_ACTIVE_MODE = args.progress

    # Set global cache configuration
    init_cache(args.disable_cache, args.cache_time)

    # Parse additional headers if provided
    additional_headers = []
    if args.add_headers:
        additional_headers = [h.strip() for h in args.add_headers.split(",")]

    # Determine output columns
    fieldnames: List[str] = DEFAULT_COLUMNS.copy() if not args.columns else args.columns.split(",")

    # Add additional header columns if specified
    for header_name in additional_headers:
        column_name = f"header_{header_name.lower().replace('-', '_')}"
        if column_name not in fieldnames:
            fieldnames.append(column_name)

    # Check if fieldnames contains only valid columns (except for dynamically added header columns)
    for col in fieldnames:
        if col not in ALL_COLUMNS and not col.startswith("header_"):
            raise ValueError(f"Invalid column name: {col}")

    # Ensure all columns from the data are included in fieldnames
    # This prevents errors when using a subset of columns

    # Extract domains from all input files and concatenate them
    global DOMAINS, OUR_IPS, OUR_IPV6S  # pylint: disable=global-statement
    DOMAINS = set()

    # Check if we should treat positional arguments as domains
    if args.domains:
        # Treat positional arguments as domain names
        DOMAINS.update(args.config_files)
    # Otherwise process domains from files if any were provided
    elif args.config_files:
        for config_file in args.config_files:
            if args.format == "nginx":
                # Load NGINX configuration
                with open(config_file, "r", errors="replace") as f:
                    config = f.read()
                DOMAINS.update(extract_domains(config))
            else:  # args.format == "list"
                # Load domains from list file (handles stdin if config_file is "-")
                DOMAINS.update(read_domains_from_list(config_file))
    else:
        # If no config files and no domains provided, show error and exit
        parser.error("Either config_files or --domains with domain names must be provided")
        return

    # Create domain info with reversed domain for sorting
    domain_info: List[Tuple[str, str]] = [(domain, reverse_domain(domain)) for domain in DOMAINS]

    # Sort by reversed domain name
    domain_info.sort(key=lambda x: x[1])

    # Override with IPs from file if provided
    if args.our_ips_file:
        OUR_IPS = read_ips_from_file(args.our_ips_file)

    # Override with IPv6s from file if provided
    if args.our_ipv6s_file:
        OUR_IPV6S = read_ips_from_file(args.our_ipv6s_file)

    # Override with IPs from command line if provided (highest priority)
    if args.our_ips:
        OUR_IPS = set(args.our_ips.split(","))

    # Override with IPv6s from command line if provided (highest priority)
    if args.our_ipv6s:
        OUR_IPV6S = set(args.our_ipv6s.split(","))

    # Prepare data for the table
    data: List[Dict[str, TableData]] = []
    total_domains = len(domain_info)

    # Setup progress bar if enabled
    total_to_process = total_domains if args.max_domains is None else min(total_domains, args.max_domains)
    init_progress_bar(total=total_to_process, description="Processing domains", unit="domain")

    # Create logic context object once before the loop
    context = NginxLogicContext(
        wait_time=args.wait_time,
        header_name=args.header,
        header_re=args.header_re,
        start_protocol=args.start_protocol,
        additional_headers=additional_headers,
    )

    domain_count = 0
    for domain, _ in domain_info:
        # Check if we've reached the maximum number of domains to process
        if args.max_domains and domain_count >= args.max_domains:
            break

        # Process domain data using the function from logic.py
        row = create_domain_data(
            domain=domain,
            context=context,
            columns=fieldnames,
        )

        data.append(row)
        domain_count += 1

        # Update progress bar
        update_progress()

    # Close progress bar
    close_progress()

    # Output the data
    if args.html:
        # Generate HTML table output
        html_output = output_html(data, fieldnames)
        print(html_output)
    else:
        # Convert TableData objects to strings for CSV output
        str_data = convert_tabledata_to_str(data)

        # Filter data to only include requested columns
        filtered_data: List[Dict[str, str]] = []
        for row_dict in str_data:
            # Create a new dictionary with only the requested columns
            new_row: Dict[str, str] = {}
            for col in fieldnames:
                if col in row_dict:
                    new_row[col] = row_dict[col]
            filtered_data.append(new_row)

        # Save to CSV
        writer = csv.DictWriter(sys.stdout, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(filtered_data)

    out.end()


if __name__ == "__main__":
    main()
