---
description: Circular Import Resolution Strategies
globs: 
alwaysApply: false
---
# Circular Import Resolution Strategies

## 🎯 PROFESSIONAL APPROACH

Circular importy jsou **architektonický problém**, ne problém k maskování!

## ✅ ŘEŠENÍ PODLE PRIORITY

### 1. **Vlastní implementace** (PREFEROVÁNO)
```python
# Místo importu z jiného modulu, implementuj funk<PERSON>
def _extract_domain_from_url(url: str) -> str:
    """Local implementation to avoid circular import."""
    # Jednoduchá, testovatelná implementace
    pass
```

### 2. **Refaktoring do utils modulu**
```python
# utils.py - společné funkce bez zá<PERSON>lostí
def extract_domain_from_url(url: str) -> str:
    """Shared utility function."""
    pass

# netlib.py
from utils import extract_domain_from_url

# domainlib.py  
from utils import extract_domain_from_url
```

### 3. **TYPE_CHECKING import**
```python
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from domainlib import SomeType

def function(param: 'SomeType') -> None:
    pass
```

### 4. **Lazy import** (pouze pokud nutné)
```python
def function_that_needs_import():
    from domainlib import needed_function  # Pouze zde!
    return needed_function()
```

## 🚫 NIKDY NEDĚLAT

```python
# ❌ Maskování problému
from module import func  # pylint: disable=import-outside-toplevel

# ❌ Import v globálním scope s disable
import module  # pylint: disable=cyclic-import
```

## 📋 KONTROLNÍ SEZNAM

Při řešení circular importu:

1. ✅ Analyzuj závislosti - kdo potřebuje co?
2. ✅ Najdi nejjednodušší řešení (vlastní implementace?)
3. ✅ Refaktoruj architekturu pokud nutné
4. ✅ Napiš testy pro nové funkce
5. ✅ Ověř, že `mise run all` prochází

## 🏗️ ARCHITEKTONICKÉ PRINCIPY

- **Single Responsibility** - každý modul má jednu odpovědnost
- **Dependency Inversion** - závislosti směřují nahoru
- **Interface Segregation** - malé, specifické rozhraní
- **Don't Repeat Yourself** - ale ne za cenu circular importů

## 📚 PŘÍKLADY Z PROJEKTU

### Problém: netlib.py potřebuje get_domain_from_url z domainlib.py
```python
# ❌ ŠPATNĚ
from domainlib import get_domain_from_url  # circular import!

# ✅ SPRÁVNĚ - vlastní implementace
def _extract_domain_from_url(url: str) -> str:
    """Extract domain from URL without external dependencies."""
    if not url:
        return ""
    
    if "://" in url:
        url = url.split("://", 1)[1]
    elif url.startswith("//"):
        url = url[2:]
    elif url.startswith("/"):
        return ""
    
    return url.split("/", 1)[0]
```

---

**Pamatuj: Circular import je signál, že architektura potřebuje zlepšení!**
