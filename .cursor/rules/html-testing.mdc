---
description: 
globs: 
alwaysApply: false
---
# HTML Testování s MCP Playwright

## 🎯 ÚČEL

Definuje standardy pro testování HTML výstupu aplikace pomocí MCP Playwright nástroje s lokálním HTTP serverem.

## 🌐 WORKFLOW HTML TESTOVÁNÍ

### ✅ PŘÍPRAVA TESTOVACÍHO PROSTŘEDÍ

1. **Generování HTML výstupu**
   ```bash
   mise run c-html
   ```
   - Vygeneruje nový `c.out.html` soubor
   - Obsahuje aktuální výstup aplikace pro testování

2. **Spuštění HTTP serveru**
   ```bash
   mise run httpserver
   ```
   - Spustí lokální server na portu 8081
   - Umožní přístup k HTML souborům přes prohlížeč

3. **Přístup k testovanému HTML**
   - URL: `http://127.0.0.1:8081/c.out.html`
   - Server mus<PERSON> běžet po celou dobu testování

## 🧪 TESTOVACÍ STRATEGIE

### ✅ TYPY TESTŮ
1. **Vizuální testy** - ověření správného zobrazení
2. **Funkční testy** - testování interaktivních prvků

### 📋 KONTROLNÍ SEZNAM PŘED TESTOVÁNÍM

- [ ] `mise run c-html` úspěšně vygeneroval nový HTML
- [ ] `mise run httpserver` běží bez chyb
- [ ] URL `http://127.0.0.1:8081/c.out.html` je dostupná
- [ ] MCP Playwright je připraven k použití


## 🛠️ ŘEŠENÍ ČASTÝCH PROBLÉMŮ

### ❌ Server se nespustí
```bash
# Zkontroluj, zda port 8081 není obsazen
netstat -tulpn | grep 8081

# Případně použij jiný port v konfiguraci
```

### ❌ HTML se nevygeneroval
```bash
# Zkontroluj chyby při generování
mise run c-html --verbose

# Ověř, že všechny závislosti jsou dostupné
```

### ❌ Playwright nemůže přistoupit ke stránce
- Ověř, že server skutečně běží
- Zkontroluj firewall nastavení
- Použij `curl http://127.0.0.1:8081/c.out.html` pro test

