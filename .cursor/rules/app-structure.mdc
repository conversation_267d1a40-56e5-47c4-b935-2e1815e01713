---
description: Struktura Aplikace - MUSÍŠ číst pokud si nejsi jistý
globs: 
alwaysApply: false
---
# Struktura Aplikace

## 🎯 ÚČEL

Definuje standardy pro organizaci kódu, modularitu a architektonické principy v Python CLI aplikacích.

## 🏗️ ARCHITEKTONICKÉ PRINCIPY

### ✅ ZÁKLADNÍ PRAVIDLA
1. **Single Responsibility Principle** - kaž<PERSON>ý modul má jednu jasnou odpovědnost
2. **Dependency Inversion** - závislosti směřují nahoru v hierarchii
3. **Interface Segregation** - malá, specifická rozhraní
4. **Don't Repeat Yourself** - ale ne za cenu circular importů

### 📁 STRUKTURA ADRESÁŘŮ
```
project/
├── main_script.py          # Entry point
├── lib/                    # Core business logic
│   ├── domainlib.py       # Domain-specific operations
│   ├── netlib.py          # Network operations
│   └── logic.py           # Main application logic
├── utils/                  # Shared utilities (no dependencies)
│   ├── cache.py           # Caching utilities
│   └── common.py          # Common helper functions
├── templates/             # Configuration templates
├── tests/                 # Test files
│   ├── test_*.py         # Unit tests
│   └── conftest.py       # Pytest configuration
├── requirements.txt       # Dependencies
├── pyproject.toml        # Project configuration
└── README.md             # Documentation
```

## 🔄 ZÁVISLOSTI MEZI MODULY

### ✅ POVOLENÉ ZÁVISLOSTI
```
main_script.py → logic.py → domainlib.py, netlib.py
                         → utils/*
domainlib.py → utils/*
netlib.py → utils/*
```

### 🚫 ZAKÁZANÉ ZÁVISLOSTI
- Circular imports mezi lib/ moduly
- Import main_script.py z jiných modulů
- Cross-dependencies mezi domainlib.py ↔ netlib.py

## 📋 KONTROLNÍ SEZNAM STRUKTURY

### ✅ PŘED PŘIDÁNÍM NOVÉHO MODULU
- [ ] Má modul jasnou, jedinou odpovědnost?
- [ ] Nevytváří circular import?
- [ ] Je umístěn ve správném adresáři?
- [ ] Má odpovídající test soubor?

### ✅ PŘED PŘIDÁNÍM NOVÉ FUNKCE
- [ ] Patří do existujícího modulu?
- [ ] Nebo potřebuje nový modul?
- [ ] Je testovatelná izolovaně?
- [ ] Má jasné rozhraní?

## 🔧 ŘEŠENÍ PROBLÉMŮ

### Circular Import
1. **Preferováno**: Vlastní implementace funkce
2. **Alternativa**: Refaktoring do utils/
3. **Poslední možnost**: TYPE_CHECKING import

### Sdílené Funkce
- Umísti do `utils/` pokud nemají závislosti
- Vytvoř nový modul v `lib/` pokud mají business logiku

### Konfigurace
- Globální konfigurace v main_script.py
- Lokální konfigurace v příslušných modulech
- Templates v `templates/` adresáři

## 🎯 PŘÍKLADY SPRÁVNÉ STRUKTURY

### ✅ DOBRÝ PŘÍKLAD
```python
# utils/url_parser.py
def extract_domain_from_url(url: str) -> str:
    """Pure utility function without dependencies."""
    pass

# lib/netlib.py
from utils.url_parser import extract_domain_from_url

# lib/domainlib.py  
from utils.url_parser import extract_domain_from_url
```

### 🚫 ŠPATNÝ PŘÍKLAD
```python
# lib/netlib.py
from lib.domainlib import get_domain  # Circular import!

# lib/domainlib.py
from lib.netlib import check_connectivity  # Circular import!
```
