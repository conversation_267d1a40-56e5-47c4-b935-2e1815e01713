#!/usr/bin/env python3

import unittest

from nginx_domain_review import DOMAINS, NginxLogicContext, extract_domains


class TestLogicContext(unittest.TestCase):
    """Test cases for the LogicContext implementation."""

    def setUp(self):
        """Set up test environment."""
        # Clear and populate the global DOMAINS set
        DOMAINS.clear()
        DOMAINS.add("example.com")
        DOMAINS.add("google.com")
        DOMAINS.add("*.example.org")

        # Create a context for testing
        self.context = NginxLogicContext()

    def test_check_in_our_domains_direct_match(self):
        """Test direct domain matching."""
        self.assertTrue(self.context.check_in_our_domains("example.com"))
        self.assertTrue(self.context.check_in_our_domains("google.com"))
        self.assertFalse(self.context.check_in_our_domains("unknown.com"))

    def test_check_in_our_domains_wildcard_match(self):
        """Test wildcard domain matching."""
        # Direct match for wildcard domain
        self.assertTrue(self.context.check_in_our_domains("*.example.org"))

        # www subdomain should match *.example.org
        self.assertTrue(self.context.check_in_our_domains("www.example.org"))

        # Other subdomains should not match
        self.assertTrue(self.context.check_in_our_domains("sub.example.org"))

        # www subdomain of non-wildcard domain should not match
        self.assertFalse(self.context.check_in_our_domains("www.example.com"))


class TestDomainExtraction(unittest.TestCase):
    """Test cases for domain extraction from nginx config."""

    def test_extract_domains_multiple_server_name_directives(self):
        """Test extracting domains from multiple server_name directives in a single server block."""
        config = """
        server {
            server_name www.elemento.cz elemento.cz;
            server_name www.frejkovy-dny.cz frejkovy-dny.cz;
        }
        """
        domains = extract_domains(config)
        self.assertEqual(len(domains), 4)
        self.assertIn("www.elemento.cz", domains)
        self.assertIn("elemento.cz", domains)
        self.assertIn("www.frejkovy-dny.cz", domains)
        self.assertIn("frejkovy-dny.cz", domains)

    def test_extract_domains_multiline_server_name(self):
        """Test extracting domains from multiline server_name directive."""
        config = """
        server {
            server_name www.example.com
                        example.com
                        api.example.com;
            server_name www.test.com
                        test.com;
        }
        """
        domains = extract_domains(config)
        self.assertEqual(len(domains), 5)
        self.assertIn("www.example.com", domains)
        self.assertIn("example.com", domains)
        self.assertIn("api.example.com", domains)
        self.assertIn("www.test.com", domains)
        self.assertIn("test.com", domains)

    def test_extract_domains_mixed_single_and_multiline(self):
        """Test extracting domains from mixed single-line and multiline server_name directives."""
        config = """
        server {
            server_name single.example.com;
            server_name multi1.example.com
                        multi2.example.com
                        multi3.example.com;
            server_name another.example.com;
        }
        """
        domains = extract_domains(config)
        self.assertEqual(len(domains), 5)
        self.assertIn("single.example.com", domains)
        self.assertIn("multi1.example.com", domains)
        self.assertIn("multi2.example.com", domains)
        self.assertIn("multi3.example.com", domains)
        self.assertIn("another.example.com", domains)

    def test_extract_domains_complex_multiline_with_comments(self):
        """Test extracting domains from complex multiline server_name with various formatting."""
        config = """
        # Main server block
        server {
            # Primary domains
            server_name www.example.com
                        example.com
                        api.example.com
                        cdn.example.com;
            
            # Secondary domains with different formatting
            server_name   test1.example.com   test2.example.com
                          test3.example.com;
            
            # Single line for comparison
            server_name single.example.com;
            
            # Default server (should be ignored)
            server_name _;
        }
        
        # Another server block
        server {
            server_name other.domain.com
                        www.other.domain.com;
        }
        """
        domains = extract_domains(config)
        expected_domains = {
            "www.example.com",
            "example.com",
            "api.example.com",
            "cdn.example.com",
            "test1.example.com",
            "test2.example.com",
            "test3.example.com",
            "single.example.com",
            "other.domain.com",
            "www.other.domain.com",
        }
        self.assertEqual(len(domains), len(expected_domains))
        for domain in expected_domains:
            self.assertIn(domain, domains)
        # Verify that '_' is not included
        self.assertNotIn("_", domains)


if __name__ == "__main__":
    unittest.main()
