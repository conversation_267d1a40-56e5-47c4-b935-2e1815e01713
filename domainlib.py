#!/usr/bin/env python3

from typing import List, Optional, Set

from netlib import (
    _make_http_get_request,
)
from out import log_debug, log_error


def reverse_domain(domain: str) -> str:
    """Reverses domain parts (e.g., www.example.com -> com.example.www)."""
    # Handle wildcard domains by moving * to the end
    if domain.startswith("*"):
        # Remove * and the following dot, will add back at the end
        domain = domain[2:]
        is_wildcard = True
    else:
        is_wildcard = False

    # Split by dots and reverse
    parts = domain.split(".")
    reversed_parts = parts[::-1]

    # Add back wildcard if needed
    if is_wildcard:
        reversed_parts.append("*")

    return ".".join(reversed_parts)


def normalize_domain(domain: str) -> str:
    """Return domain name from various formats to format.
    Used to transform domain match from nginx to real domain.
    Used to extract domain from URL.
    """
    domain = domain.lower()
    if domain.startswith("*."):
        domain = "www." + domain[2:]
    if domain.startswith("//"):
        domain = domain[2:]
    if domain.startswith("/"):
        log_error(f"Cannot normalize relative URL as domain, starts with /: {domain}")
        domain = "unknown" + domain
    if domain.startswith("http://"):
        domain = domain[7:]
    if domain.startswith("https://"):
        domain = domain[8:]
    if "/" in domain:
        domain = domain.split("/")[0]
    return domain


def get_base_domain(domain: str) -> str:
    """Return base domain from domain name."""
    # LATER Add support for other logic based on TLD
    return ".".join(domain.rsplit(".", 2)[-2:])


def get_protocol_from_url(url: str) -> str:
    """Return protocol from URL."""
    if "://" not in url:
        return "http"
    return url.split("://")[0]


def get_domain_from_url(url: str) -> Optional[str]:
    """Return domain with optional port from URL in any format."""

    # Protocol seaparation
    if "://" in url:
        url = url.split("://", 1)[1]
    elif url.startswith("//"):
        url = url[2:]
    elif url.startswith("/"):
        return None
    else:
        # LATER distinguish between relative and domain?
        return None

    # Extract domain
    domain = url.split("/", 1)[0]

    return domain


def get_uri_from_url(url: str) -> str:
    """Return url withou optional query and fragment."""
    return url.split("?")[0]


def resolve_domain(url: str, base: str) -> str:
    """Resolve url to absolute URL. Use base for relative URLs."""
    if url.startswith("//"):
        protocol = get_protocol_from_url(base)
        return f"{protocol}:{url}"
    if url.startswith("/"):
        protocol = get_protocol_from_url(base)
        base_domain = get_domain_from_url(base)
        return f"{protocol}://{base_domain}{url}"
    if url.startswith("http://") or url.startswith("https://"):
        return url
    base_uri = get_uri_from_url(url)
    return f"{base_uri}/{url}"


def is_same_domain(d1: str, d2: str) -> bool:
    """Checks if two domain name belogn to the same domain."""
    # Normalize domains to lowercase
    d1 = normalize_domain(d1)
    d2 = normalize_domain(d2)

    # Exact match
    if d1 == d2:
        return True

    if d1.endswith("." + d2):
        return True

    if d2.endswith("." + d1):
        return True

    # Leve only last two domain parts joined
    b1 = get_base_domain(d1)
    b2 = get_base_domain(d2)

    return b1 == b2


# pylint: disable=too-few-public-methods
class RedirectResult:
    """Class representing the result of a redirect chain.

    Attributes:
        final_domain: The domain part of the final URL in the redirect chain
        is_redirected: Whether the final domain is different from the original
        redirect_chain: List of URLs in the redirect chain
        final_protocol: Protocol of the final URL
        error: Whether an error occurred during the redirect chain
        error_message: Error message if an error occurred
        final_status_code: Status code of the final request in the redirect chain
    """

    def __init__(
        self,
        final_domain: Optional[str] = "",
        is_redirected: bool = False,
        redirect_chain: Optional[List[str]] = None,
        final_protocol: str = "http",
        error: bool = False,
        error_message: Optional[str] = None,
        final_status_code: Optional[int] = None,
    ):
        self.final_domain = final_domain
        self.is_redirected = is_redirected
        self.redirect_chain = redirect_chain or []
        self.final_protocol = final_protocol
        self.error = error
        self.error_message = error_message
        self.final_status_code = final_status_code

    def __str__(self) -> str:
        """String representation of the redirect result."""
        if self.error:
            return f"RedirectError: {self.error_message}"
        if self.is_redirected:
            return f"Redirected to {self.final_protocol}://{self.final_domain}"
        return f"No redirect: {self.final_protocol}://{self.final_domain}"


def follow_redirects(
    domain_name: str,
    max_redirects: int = 10,
    wait_time: Optional[float] = None,
    start_protocol: str = "http",
) -> RedirectResult:
    """Follows a chain of redirects and returns the final destination domain.
    Individual HTTP GET requests are cached.

    Args:
        domain_name: The domain name to check
        max_redirects: Maximum number of redirects to follow
        wait_time: Time to wait between HTTP requests
        start_protocol: Protocol to start with (http or https)

    Returns:
        RedirectResult object
    """

    domain_name_for_http = normalize_domain(domain_name)

    redirect_chain: List[str] = []
    current_url = f"{start_protocol}://{domain_name_for_http}"

    final_domain_in_chain: str = ""  # The domain part of the very last URL in the chain
    final_protocol_in_chain: str = start_protocol  # Protocol of the very last URL

    redirect_count = 0
    visited_urls: Set[str] = set()

    while redirect_count < max_redirects:

        # Check for circular redirects
        if current_url in visited_urls:
            log_debug(f"Circular redirect detected at {current_url}")
            final_domain_in_chain = "LOOP"
            redirect_chain.append(final_domain_in_chain)
            return RedirectResult(
                final_domain=final_domain_in_chain,
                is_redirected=True,
                redirect_chain=redirect_chain,
                final_protocol=final_protocol_in_chain,
                error=True,
                error_message=f"Circular redirect detected at {current_url}",
                final_status_code=None,
            )

        redirect_chain.append(current_url)
        visited_urls.add(current_url)

        # Make the HTTP request using the new cached helper
        # We manually follow redirects, so allow_redirects=False for _make_http_get_request
        response_data = _make_http_get_request(
            current_url,
            timeout=30,
            allow_redirects=False,
            wait_time=wait_time,
        )

        # Abort on error
        if not response_data or response_data.get("error"):
            log_debug(
                f"Failed to get response or received error for {current_url} in follow_redirects. Error: {response_data.get('error_message') if response_data else 'No response'}"
            )
            final_domain_in_chain = "ERROR"
            redirect_chain.append(final_domain_in_chain)
            return RedirectResult(
                final_domain=final_domain_in_chain,
                is_redirected=True,
                redirect_chain=redirect_chain,
                final_protocol=final_protocol_in_chain,
                error=True,
                error_message=response_data.get("error_message") if response_data else "No response",
                final_status_code=response_data.get("status_code"),
            )

        status_code = response_data["status_code"]
        headers = response_data["headers"]

        log_debug(f"Follow redirects: URL={current_url}, Status={status_code}, Headers={headers}")

        if status_code in (301, 302, 303, 307, 308) and "Location" in headers:
            redirect_url_from_header = headers["Location"]

            resolved_next_url = resolve_domain(redirect_url_from_header, current_url)
            final_domain_in_chain = get_domain_from_url(resolved_next_url) or ""
            final_protocol_in_chain = get_protocol_from_url(resolved_next_url)

            current_url = resolved_next_url
            redirect_count += 1
        else:
            break

    # Check if the final domain is different from the original domain
    # If the redirect is to a path on the same domain, it's not considered a redirect
    is_redirected_from_original = redirect_count > 0 and not is_same_domain(domain_name_for_http, final_domain_in_chain)

    return RedirectResult(
        final_domain=final_domain_in_chain,
        is_redirected=is_redirected_from_original,
        redirect_chain=redirect_chain,
        final_protocol=final_protocol_in_chain,
        final_status_code=status_code,
    )
