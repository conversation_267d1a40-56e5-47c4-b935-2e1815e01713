---
description: 
globs: *.py
alwaysApply: false
---
# Python Code Quality Standards

## ABSOLUTE PROHIBITIONS

### 🚫 NEVER USE THESE ANTI-PATTERNS

1. **Import outside toplevel** - ZAKÁZÁNO!
   ```python
   # ❌ ŠPATNĚ - NIKDY NEDĚLAT!
   def some_function():
       from some_module import something  # PRASÁRNA!
   ```

2. **Pylint disable komentáře** - ZAKÁZÁNO!
   ```python
   # ❌ ŠPATNĚ - NIKDY NEDĚLAT!
   from module import func  # pylint: disable=import-outside-toplevel  # PRASÁRNA!
   ```

3. **Maskování lint chyb** místo jejich řešení - ZAKÁZÁNO!

## ✅ SPRÁVNÉ ŘEŠENÍ CIRCULAR IMPORTŮ

### Metoda 1: Vlastní implementace funkce
```python
# ✅ SPRÁVNĚ - vytvořit vlastní funkci místo importu
def _extract_domain_from_url(url: str) -> str:
    """Extract domain from URL without external dependencies."""
    if not url:
        return ""
    
    if "://" in url:
        url = url.split("://", 1)[1]
    elif url.startswith("//"):
        url = url[2:]
    elif url.startswith("/"):
        return ""
    
    return url.split("/", 1)[0]
```

### Metoda 2: Refaktoring architektury
- Přesunout sdílené funkce do samostatného modulu
- Vytvořit utils modul pro společné funkce
- Reorganizovat závislosti

### Metoda 3: TYPE_CHECKING import
```python
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from some_module import SomeType
```

## 📋 POVINNÉ KONTROLY

### Před každým commitem MUSÍ projít:
1. **pylint** - rating 10.00/10
2. **mypy** - žádné chyby
3. **black** - formátování
4. **isort** - import sorting
5. **pytest** - všechny testy

### Spuštění kontrol:
```bash
mise run all  # MUSÍ projít bez chyb!
```

## 🎯 KVALITNÍ KÓD ZNAMENÁ

- **Čitelnost** - kód čte více lidí než ho píše
- **Udržovatelnost** - změny jsou snadné a bezpečné
- **Testovatelnost** - každá funkce má testy
- **Dokumentace** - docstringy pro všechny public funkce
- **Konzistence** - dodržování standardů projektu

## ⚠️ DŮSLEDKY PORUŠENÍ

Pokud někdo:
- Použije import-outside-toplevel
- Přidá pylint disable komentář
- Zamaskuje lint chyby místo jejich řešení

**→ Kód se OKAMŽITĚ opravuje podle těchto pravidel!**

## 📚 REFERENCE

- [PEP 8](mdc:https:/pep8.org) - Python Style Guide
- [Google Python Style Guide](mdc:https:/google.github.io/styleguide/pyguide.html)
- [Real Python Best Practices](mdc:https:/realpython.com/python-code-quality)

---

**Pamatuj: Kvalitní kód je investice do budoucnosti. Prasárny jsou dluh, který se musí splácet.**
