#!/usr/bin/env python3

import unittest
from unittest.mock import patch

from cache import init_cache

# Import cache functions for testing
from domainlib import follow_redirects, is_same_domain, normalize_domain

init_cache(disable=True)


class TestWildcardRedirects(unittest.TestCase):
    """Test cases for wildcard domain handling in redirects."""

    def _test_redirect_scenario(self, domain, mock_responses, expected_result):
        """Helper method to test redirect scenarios.

        Args:
            domain: Domain to test
            mock_responses: List of mock responses for _make_http_get_request
            expected_result: Dict with expected results (final_domain, was_redirected, etc.)
        """
        with patch("domainlib._make_http_get_request") as mock_http_get:
            # Configure mock to return different responses based on URL
            def side_effect(url, **_):
                for response in mock_responses:
                    if response["url"] == url:
                        return response
                return {"error": True, "status_code": None, "headers": {}, "url": url}

            mock_http_get.side_effect = side_effect

            # Call the function being tested
            result = follow_redirects(domain)

            # Assert the results
            self.assertEqual(result.final_domain, expected_result["final_domain"])
            self.assertEqual(result.is_redirected, expected_result["was_redirected"])
            self.assertEqual(result.redirect_chain, expected_result["redirect_chain"])
            self.assertEqual(result.final_protocol, expected_result["final_protocol"])

    def test_redirect_within_domain(self):
        """Test handling of wildcard domain *.example.com redirecting to www.example.org."""
        self._test_redirect_scenario(
            domain="example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "https://www.example.com/"},
                    "url": "http://example.com",
                },
                {
                    "error": False,
                    "status_code": 200,  # Non-redirect status
                    "headers": {"Content-Type": "text/html"},
                    "url": "https://www.example.com/",
                },
            ],
            expected_result={
                "final_domain": "www.example.com",
                "was_redirected": False,
                "redirect_chain": ["http://example.com", "https://www.example.com/"],
                "final_protocol": "https",
            },
        )

    def test_redirect_with_wildcard(self):
        """Test handling of wildcard domain *.example.com redirecting to www.example.org."""
        self._test_redirect_scenario(
            domain="*.example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "https://www.example.org/"},
                    "url": "http://www.example.com",
                },
                {
                    "error": False,
                    "status_code": 200,  # Non-redirect status
                    "headers": {"Content-Type": "text/html"},
                    "url": "https://www.example.org/",
                },
            ],
            expected_result={
                "final_domain": "www.example.org",
                "was_redirected": True,
                "redirect_chain": [
                    "http://www.example.com",
                    "https://www.example.org/",
                ],
                "final_protocol": "https",
            },
        )

    def test_redirect(self):
        """Test scenario where a domain redirects."""
        self._test_redirect_scenario(
            domain="www.example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "https://www.example.org"},
                    "url": "http://www.example.com",
                },
                {
                    "error": False,
                    "status_code": 200,  # Non-redirect status
                    "headers": {"Content-Type": "text/html"},
                    "url": "https://www.example.org",
                },
            ],
            expected_result={
                "final_domain": "www.example.org",
                "was_redirected": True,
                "redirect_chain": ["http://www.example.com", "https://www.example.org"],
                "final_protocol": "https",
            },
        )

    def test_redirect_loop1(self):
        """Test scenario where a domain redirects."""
        self._test_redirect_scenario(
            domain="www.example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "http://www.example.com"},
                    "url": "http://www.example.com",
                }
            ],
            expected_result={
                "final_domain": "LOOP",
                "was_redirected": True,
                "redirect_chain": [
                    "http://www.example.com",
                    "LOOP",
                ],
                "final_protocol": "http",
            },
        )

    def test_redirect_loop2(self):
        """Test scenario where a domain redirects."""
        self._test_redirect_scenario(
            domain="www.example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "https://www.example.com/"},
                    "url": "http://www.example.com",
                },
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "http://www.example.com"},
                    "url": "https://www.example.com/",
                },
            ],
            expected_result={
                "final_domain": "LOOP",
                "was_redirected": True,
                "redirect_chain": [
                    "http://www.example.com",
                    "https://www.example.com/",
                    "LOOP",
                ],
                "final_protocol": "http",
            },
        )

    def test_redirect_error(self):
        """Test the issue with wildcard domains in the redirect column."""
        self._test_redirect_scenario(
            domain="sub.example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "https://www.example.org/"},
                    "url": "http://sub.example.com",
                },
                {
                    "error": True,
                    "status_code": 500,
                    "headers": None,
                    "url": "https://www.example.org/",
                },
            ],
            expected_result={
                "final_domain": "ERROR",
                "was_redirected": True,
                "redirect_chain": [
                    "http://sub.example.com",
                    "https://www.example.org/",
                    "ERROR",
                ],
                "final_protocol": "https",
            },
        )

    def test_redirect_to_path(self):
        """Test scenario where a domain redirects to a path on the same domain."""
        self._test_redirect_scenario(
            domain="www.example.com",
            mock_responses=[
                {
                    "error": False,
                    "status_code": 301,
                    "headers": {"Location": "/page.html"},
                    "url": "http://www.example.com",
                },
                {
                    "error": False,
                    "status_code": 200,
                    "headers": {"Content-Type": "text/html"},
                    "url": "http://www.example.com/page.html",
                },
            ],
            expected_result={
                "final_domain": "www.example.com",
                "was_redirected": False,  # Should not be considered a redirect
                "redirect_chain": ["http://www.example.com", "http://www.example.com/page.html"],
                "final_protocol": "http",
            },
        )


class TestIsSameDomain(unittest.TestCase):
    """Test cases for the is_same_domain function."""

    def test_is_same_domain(self):
        """Test the is_same_domain function."""

        # Test exact domain matches
        self.assertTrue(is_same_domain("example.com", "example.com"))
        self.assertTrue(is_same_domain("www.example.com", "www.example.com"))
        self.assertTrue(is_same_domain("www.example.com", "example.com"))
        self.assertTrue(is_same_domain("example.com", "www.example.com"))

        # Test wildcard matches
        self.assertTrue(is_same_domain("sub.example.com", "*.example.com"))
        self.assertTrue(is_same_domain("www.example.com", "*.example.com"))
        self.assertTrue(is_same_domain("example.com", "*.example.com"))

        # Test other subdomain matches
        self.assertTrue(is_same_domain("sub1.example.com", "sub2.example.com"))
        self.assertTrue(is_same_domain("sub.sub1.example.com", "sub2.example.com"))

        # Test URLs matching
        self.assertTrue(is_same_domain("example.com", "//example.com"))
        self.assertTrue(is_same_domain("example.com", "https://example.com"))
        self.assertTrue(is_same_domain("http://example.com", "https://example.com"))
        self.assertTrue(is_same_domain("http://sub.example.com", "https://example.com"))
        self.assertTrue(is_same_domain("http://example.com", "https://sub.example.com"))

        # Test URLs matching with path and query
        self.assertTrue(is_same_domain("example.com", "https://example.com/a"))
        self.assertTrue(is_same_domain("example.com", "https://example.com/a/b"))
        self.assertTrue(is_same_domain("example.com", "https://example.com/a/b/c"))
        self.assertTrue(is_same_domain("example.com", "https://example.com/a/b/c?d"))
        self.assertTrue(is_same_domain("example.com", "https://example.com/a/b/c?d=1"))
        self.assertTrue(is_same_domain("example.com", "https://example.com/a/b/c?d=1&e=2"))

        # Test non-matches
        self.assertFalse(is_same_domain("example.com", "/example.com"))
        self.assertFalse(is_same_domain("example.org", "example.com"))
        self.assertFalse(is_same_domain("example.org", "*.example.com"))
        self.assertFalse(is_same_domain("sub.example.org", "*.example.com"))
        self.assertFalse(is_same_domain("http://example.org", "example.com"))
        self.assertFalse(is_same_domain("https://example.org", "*.example.com"))
        self.assertFalse(is_same_domain("//sub.example.org", "*.example.com"))


class TestDomainNormalization(unittest.TestCase):
    """Test cases for domain normalization function."""

    def test_normalize_domain(self):
        """Test the normalize_domain function with various input formats."""
        # Test basic domain
        self.assertEqual(normalize_domain("example.com"), "example.com")

        # Test wildcard domain
        self.assertEqual(normalize_domain("*.example.com"), "www.example.com")

        # Test protocol-relative URL
        self.assertEqual(normalize_domain("//example.com"), "example.com")

        # Test HTTP URL
        self.assertEqual(normalize_domain("http://example.com"), "example.com")

        # Test HTTPS URL
        self.assertEqual(normalize_domain("https://example.com"), "example.com")

        # Test URL with path
        self.assertEqual(normalize_domain("example.com/path"), "example.com")

        # Test URL with query parameters
        self.assertEqual(normalize_domain("example.com/path?query=value"), "example.com")

        # Test mixed case
        self.assertEqual(normalize_domain("ExAmPlE.CoM"), "example.com")

        # Test complex URL
        self.assertEqual(
            normalize_domain("https://Sub.ExAmPlE.CoM/path/to/page?query=value"),
            "sub.example.com",
        )


if __name__ == "__main__":
    unittest.main()
