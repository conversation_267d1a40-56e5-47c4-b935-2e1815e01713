import re
from typing import Any, Dict, List, Optional

from domainlib import (
    follow_redirects,
    normalize_domain,
    reverse_domain,
)
from netlib import _get_whois_info, _make_http_get_request, _resolve_dns_record

SPECIAL_TLDS = ["co.uk", "org.uk", "net.uk", "ac.uk", "gov.uk", "com.au", "net.au", "org.au", "co.nz", "co.jp"]


# All possible output columns
ALL_COLUMNS: List[str] = [
    "domain",
    "reversed_domain",
    "ip",
    "ipv6",
    "cname",
    "our",
    "our_redirect",
    "redirect",
    "https",
    "https_redirect",
    "header",
    "cdn",
    "cdn_provider",
    "whois",
    "http_code",
]

# Default columns (WHOIS is excluded by default)
DEFAULT_COLUMNS: List[str] = [
    "domain",
    "reversed_domain",
    "ip",
    "ipv6",
    "cname",
    "our",
    "our_redirect",
    "redirect",
    "https",
    "https_redirect",
    "header",
    "cdn",
    "cdn_provider",
    "http_code",
]


# pylint: disable=too-few-public-methods
class TableData:
    """Class representing a table cell with value and optional popup information.

    This class is used to store both the main value and additional popup information
    that will be displayed when hovering over the cell in the HTML output.

    Attributes:
        value: The main value to display in the table cell
        popup: Optional additional information to display in a popup on hover
    """

    def __init__(self, value: str, popup: Optional[str] = None):
        """Initialize a TableData object.

        Args:
            value: The main value to display
            popup: Optional additional information for popup
        """
        self.value = value
        self.popup = popup

    def __str__(self) -> str:
        """Return the string representation of the TableData object.

        Returns:
            str: The main value as a string
        """
        return self.value


# Common CDN CNAME patterns for CNAME detection
CDN_PATTERNS: Dict[str, str] = {
    "cloudflare": r"(cloudflare|cloudfront|cdn\.cloudflare)\.com$",
    "akamai": r"(akamai|akamaiedge|akamaitechnologies)\.net$",
    "fastly": r"(fastly|global\.fastly)\.net$",
    "cloudfront": r"cloudfront\.net$",
    "maxcdn": r"maxcdn\.com$",
    "keycdn": r"kxcdn\.com$",
    "jsdelivr": r"jsdelivr\.net$",
    "cachefly": r"cachefly\.net$",
    "cdnetworks": r"cdnetworks\.com$",
    "azureedge": r"azureedge\.net$",
    "edgecast": r"edgecastcdn\.net$",
    "limelight": r"(limelight|llnwd)\.net$",
    "stackpath": r"(stackpath|stackpathdns)\.com$",
    "incapsula": r"incapdns\.net$",
    "sucuri": r"sucuri\.net$",
    "imperva": r"imperva\.com$",
    "gstatic": r"gstatic\.com$",
}

# Common CDN CNAME patterns for Server header detection
CDN_SERVER_PATTERNS: Dict[str, str] = {
    "cloudflare": r"cloudflare",
    "akamai": r"(akamai|akamaiGHost)",
    "fastly": r"(fastly|varnish)",
    "cloudfront": r"cloudfront",
    "sucuri": r"sucuri",
    "imperva": r"imperva",
    "incapsula": r"incapsula",
    "limelight": r"limelight",
    "maxcdn": r"maxcdn",
    "keycdn": r"keycdn",
    "edgecast": r"edgecast",
    "azure": r"(azure|microsoft)",
    "google": r"(google|gws)",
    "amazon": r"(amazon|aws)",
}


class LogicContext:
    """Abstract base class for domain processing context with methods for checking IPs and domains."""

    def __init__(
        self,
        wait_time: Optional[float] = None,
        header_name: Optional[str] = None,
        header_re: Optional[str] = None,
        start_protocol: str = "http",
        additional_headers: Optional[List[str]] = None,
    ):
        """Initialize the logic context with configuration parameters.

        Args:
            wait_time: Time to wait between HTTP requests
            header_name: Name of the header to check
            header_re: Regular expression for header value
            start_protocol: Protocol to start with (http or https)
            additional_headers: List of additional headers to include in the output
        """
        self.wait_time = wait_time
        self.header_name = header_name
        self.header_re = header_re
        self.start_protocol = start_protocol
        self.additional_headers = additional_headers or []

    def check_in_our_ips(self, ip: str) -> bool:
        """Check if an IP is in our IPs set.

        Args:
            ip: The IP address to check

        Returns:
            bool: True if the IP is in our IPs set, False otherwise
        """
        raise NotImplementedError("Subclasses must implement check_in_our_ips")

    def check_in_our_ipv6s(self, ipv6: str) -> bool:
        """Check if an IPv6 is in our IPv6s set.

        Args:
            ipv6: The IPv6 address to check

        Returns:
            bool: True if the IPv6 is in our IPv6s set, False otherwise
        """
        raise NotImplementedError("Subclasses must implement check_in_our_ipv6s")

    def check_in_our_domains(self, domain: str) -> bool:
        """Check if a domain is in our domains set.

        Args:
            domain: The domain to check

        Returns:
            bool: True if the domain is in our domains set, False otherwise
        """
        raise NotImplementedError("Subclasses must implement check_in_our_domains")


def match_patterns(name: str, patterns: Dict[str, str]) -> Optional[str]:
    """Detects the CDN provider based on patterns."""
    if not name:
        return None

    for provider, pattern in patterns.items():
        if re.search(pattern, name, re.IGNORECASE):
            return provider

    return None


def process_domain_logic(
    domain: str,
    dns_a: Dict[str, List[str]],
    dns_aaaa: Dict[str, List[str]],
    dns_cname: Dict[str, List[str]],
    http_direct: Dict[str, Any],
    https_direct: Dict[str, Any],
    redirect_result: Any,
    context: LogicContext,
    whois_info: Optional[Dict[str, Any]] = None,
) -> Dict[str, TableData]:
    """Process domain logic with provided data and return a dictionary with TableData objects.

    This internal function contains the pure logic for processing domain data.
    It's separated to make testing easier.

    Args:
        domain: The original domain
        dns_a: DNS A record results
        dns_aaaa: DNS AAAA record results
        dns_cname: DNS CNAME record results
        redirect_result: Result from follow_redirects function
        https_direct: Result from HTTPS request
        http_direct: Result from HTTP request
        context: LogicContext object with configuration and methods
        whois_info: Optional WHOIS information for the domain, only provided when the whois column is requested

    Returns:
        dict: Dictionary with TableData objects for each column
    """

    reversed_domain = reverse_domain(domain)

    row: Dict[str, TableData] = {
        "domain": TableData(domain),
        "reversed_domain": TableData(reversed_domain),
    }
    for col in ALL_COLUMNS:
        if col not in row:
            row[col] = TableData("")

    def get_ip_table_data(dns_data: Dict[str, Any]) -> TableData:
        """Helper function to get IP/IPv6 TableData from DNS results."""
        ips = ""
        if dns_data["records"]:
            ips = ",".join(dns_data["records"])
        if dns_data.get("error", False):
            ips += f" ({dns_data['error_type']})"
        return TableData(ips, f"{dns_data['error_message']}" if dns_data.get("error", False) else None)

    # Process IP and IPv6 addresses
    row["ip"] = get_ip_table_data(dns_a)
    row["ipv6"] = get_ip_table_data(dns_aaaa)

    # If no IP and no IPv6, return the basic row
    if dns_a["records"] == [] and dns_aaaa["records"] == []:
        return row

    # Add CNAME if available
    cname_value = dns_cname["records"][0] if dns_cname["records"] else ""
    row["cname"] = TableData(
        cname_value,
        (f"{dns_cname['error_type']}: {dns_cname['error_message']}" if dns_cname.get("error", False) else None),
    )

    # Detect direct pointing to us (either via IPv4 or IPv6)
    leads_directly_ipv4 = any(context.check_in_our_ips(ip) for ip in dns_a["records"])
    leads_directly_ipv6 = any(context.check_in_our_ipv6s(ipv6) for ipv6 in dns_aaaa["records"])
    leads_directly = leads_directly_ipv4 or leads_directly_ipv6

    if context.start_protocol == "https":
        http_direct = https_direct

    # Get header value if requested
    header_val = None
    if context.header_name:
        header_val = http_direct["headers"].get(context.header_name, None)
        if header_val:
            header_popup = f"{context.header_name}: {header_val}"
            row["header"] = TableData(header_val, header_popup)

    # Add additional headers if requested
    for header_name in context.additional_headers:
        # Create a column name based on the header name (lowercase with hyphens replaced by underscores)
        column_name = f"header_{header_name.lower().replace('-', '_')}"
        header_value = http_direct["headers"].get(header_name, "")
        row[column_name] = TableData(header_value)

    # Detect CDN and indirect pointing
    cdn_provider = None
    leads_indirect = False
    cdn_popup = ""

    if not leads_directly:
        # Try detect CDN from Server header or CNAME
        server_header = http_direct["headers"].get("Server", "")
        if server_header:
            cdn_provider = match_patterns(server_header, CDN_SERVER_PATTERNS)
            if cdn_provider:
                cdn_popup += f"Detected from Server header: {server_header}"

        if not cdn_provider and cname_value:
            cdn_provider = match_patterns(cname_value, CDN_PATTERNS)
            if cdn_provider:
                cdn_popup += f"Detected from CNAME: {cname_value}"

        # Try detect requested header
        if context.header_name and header_val is not None:
            if context.header_re:
                leads_indirect = bool(re.search(context.header_re, header_val))
                if leads_indirect:
                    cdn_popup += f"Detected from header '{context.header_name}' value '{header_val}' matching regex: '{context.header_re}'"
            else:
                leads_indirect = True
                if header_val:
                    cdn_popup += f"Detected from header '{context.header_name}' value presence"

        cdn_value = "yes" if cdn_provider is not None or leads_indirect else "no"
        cdn_popup = cdn_popup.strip()
        row["cdn"] = TableData(cdn_value, cdn_popup if cdn_popup and cdn_popup != "" else None)

        cdn_provider_value = cdn_provider if cdn_provider is not None else ""
        row["cdn_provider"] = TableData(cdn_provider_value)

    # Set whether the domain is ours
    our_value = "yes" if leads_directly or leads_indirect else "no"
    our_popup = None
    if leads_directly:
        if leads_directly_ipv4:
            our_popup = "Domain points directly to our IPv4 address"
        else:
            our_popup = "Domain points directly to our IPv6 address"
    elif leads_indirect:
        our_popup = f"Domain is behind CDN but has our header: {context.header_name}"

    row["our"] = TableData(our_value, our_popup)

    # Add redirect information
    if redirect_result.is_redirected:
        final_domain = redirect_result.final_domain or ""
        redirect_popup = None

        if redirect_result.redirect_chain:
            redirect_chain_str = " -> ".join(redirect_result.redirect_chain)
            redirect_popup = f"Redirect chain:\n{redirect_chain_str}"

        row["redirect"] = TableData(final_domain, redirect_popup)

        our_redirect_value = "yes" if final_domain and context.check_in_our_domains(final_domain) else "no"
        our_redirect_popup = f"Final domain {final_domain} is in our domains list" if our_redirect_value == "yes" else None
        row["our_redirect"] = TableData(our_redirect_value, our_redirect_popup)

    # Populate http_code column with the status code from the final request in the redirect chain.
    final_status_code = ""
    if redirect_result.final_status_code is not None:  # Check if final_status_code exists
        final_status_code = str(redirect_result.final_status_code)
    row["http_code"] = TableData(final_status_code)

    # Set HTTPS information
    https_works = not https_direct.get("error")
    https_state = "yes" if https_works else "no"
    https_popup = None
    if https_direct.get("error"):
        https_popup = f"HTTPS error: {https_direct.get('error_message', 'Unknown error')}"
        if "expired" in https_popup:
            https_state = "EXPIRED"
        elif "mismatch" in https_popup:
            https_state = "MISMATCH"
        elif "refused" in https_popup:
            https_state = "REFUSED"
        elif "unreachable" in https_popup:
            https_state = "UNREACHABLE"
        elif "handshake" in https_popup:
            https_state = "HANDSHAKE"
        elif "timeout" in https_popup:
            https_state = "TIMEOUT"
        else:
            https_state = "ERROR"
    elif https_direct.get("status_code"):
        https_popup = f"HTTPS status: {https_direct.get('status_code')}"
    row["https"] = TableData(https_state, https_popup)

    https_redirect_value = "yes" if redirect_result.final_protocol == "https" else "no"
    row["https_redirect"] = TableData(https_redirect_value)

    # Process WHOIS information if requested and provided
    if "whois" in row and whois_info is not None:
        if whois_info.get("error", False):
            row["whois"] = TableData("error", whois_info.get("error_message", "Unknown error"))
        else:
            whois_data = whois_info.get("data", {})
            if whois_data:
                # Create a summary string for display
                registrar = whois_data.get("registrar", "")
                creation_date = whois_data.get("creation_date", "")

                summary = registrar
                if creation_date:
                    summary += f" (created: {creation_date.split()[0]})"  # Just show the date part

                # Create a detailed popup with all information
                popup_lines = []
                for key, value in whois_data.items():
                    if value:  # Only include non-empty values
                        popup_lines.append(f"{key}: {value}")

                popup = "\n".join(popup_lines)
                row["whois"] = TableData(summary, popup)
            else:
                row["whois"] = TableData("", None)

    return row


def create_domain_data(
    domain: str,
    context: LogicContext,
    columns: List[str],
) -> Dict[str, TableData]:
    """Process a single domain and return a dictionary with TableData objects.

    This function only gathers data and calls process_domain_logic.
    All processing logic is in process_domain_logic.

    WHOIS information is only retrieved if the 'whois' column is requested.
    WHOIS queries are expensive and rate-limited, so we only make them when needed.

    Args:
        domain: The domain to process
        context: LogicContext object with configuration and methods
        columns: List of columns to include in the output

    Returns:
        dict: Dictionary with TableData objects for each column
    """
    ndomain = normalize_domain(domain)

    # Only get WHOIS information if the column is requested
    whois_info = None
    if "whois" in columns:
        # Extract the second-level domain for WHOIS query
        # WHOIS should only be queried for the registrable domain, not subdomains
        whois_domain = ndomain
        for special_tld in SPECIAL_TLDS:
            if ndomain.endswith("." + special_tld):
                parts = ndomain.rsplit("." + special_tld, 1)
                if len(parts) > 1 and parts[0]:
                    last_part = parts[0].split(".")[-1]
                    whois_domain = last_part + "." + special_tld
                    whois_info = _get_whois_info(whois_domain)
                    whois_domain = ""
                break
        if whois_domain:
            if len(whois_domain.split(".")) == 2:
                whois_info = _get_whois_info(whois_domain)

    return process_domain_logic(
        domain=domain,
        dns_a=_resolve_dns_record(ndomain, "A"),
        dns_aaaa=_resolve_dns_record(ndomain, "AAAA"),
        dns_cname=_resolve_dns_record(ndomain, "CNAME"),
        redirect_result=follow_redirects(
            ndomain,
            wait_time=context.wait_time,
            start_protocol=context.start_protocol,
        ),
        http_direct=_make_http_get_request(url="http://" + ndomain, allow_redirects=False, wait_time=context.wait_time),
        https_direct=_make_http_get_request(url="https://" + ndomain, allow_redirects=False, wait_time=context.wait_time),
        whois_info=whois_info,
        context=context,
    )
